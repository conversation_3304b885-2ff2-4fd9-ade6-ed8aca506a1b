import React, { useState } from 'react';
import { X, Monitor, Grid, List, Eye, <PERSON>Off, Palette, Image } from 'lucide-react';
import { UserSettings, Category, UpdateSettingsRequest } from '../types';

interface SettingsModalProps {
  settings: UserSettings;
  categories: Category[];
  onUpdateSettings: (updates: UpdateSettingsRequest) => void;
  onClose: () => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  settings,
  categories,
  onUpdateSettings,
  onClose,
}) => {
  const [localSettings, setLocalSettings] = useState<UserSettings>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  const handleChange = (key: keyof UserSettings, value: any) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = () => {
    const updates: UpdateSettingsRequest = {};
    
    if (localSettings.theme !== settings.theme) {
      updates.theme = localSettings.theme;
    }
    if (localSettings.layout !== settings.layout) {
      updates.layout = localSettings.layout;
    }
    if (localSettings.columns_count !== settings.columns_count) {
      updates.columns_count = localSettings.columns_count;
    }
    if (localSettings.show_search !== settings.show_search) {
      updates.show_search = localSettings.show_search;
    }
    if (localSettings.show_categories !== settings.show_categories) {
      updates.show_categories = localSettings.show_categories;
    }
    if (localSettings.background_image !== settings.background_image) {
      updates.background_image = localSettings.background_image;
    }

    if (Object.keys(updates).length > 0) {
      onUpdateSettings(updates);
    }
    onClose();
  };

  const handleReset = () => {
    setLocalSettings(settings);
    setHasChanges(false);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            设置
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* 主题设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <Palette className="w-5 h-5 mr-2" />
              外观
            </h3>
            
            <div className="space-y-4">
              {/* 主题选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  主题
                </label>
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleChange('theme', 'light')}
                    className={`
                      flex-1 p-3 rounded-lg border-2 transition-all
                      ${localSettings.theme === 'light'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                      }
                    `}
                  >
                    <div className="text-center">
                      <div className="w-8 h-8 bg-white border border-gray-300 rounded mx-auto mb-2"></div>
                      <span className="text-sm font-medium">浅色</span>
                    </div>
                  </button>
                  
                  <button
                    onClick={() => handleChange('theme', 'dark')}
                    className={`
                      flex-1 p-3 rounded-lg border-2 transition-all
                      ${localSettings.theme === 'dark'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                      }
                    `}
                  >
                    <div className="text-center">
                      <div className="w-8 h-8 bg-gray-800 border border-gray-600 rounded mx-auto mb-2"></div>
                      <span className="text-sm font-medium">深色</span>
                    </div>
                  </button>
                </div>
              </div>

              {/* 布局选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  布局
                </label>
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleChange('layout', 'grid')}
                    className={`
                      flex-1 p-3 rounded-lg border-2 transition-all
                      ${localSettings.layout === 'grid'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                      }
                    `}
                  >
                    <div className="text-center">
                      <Grid className="w-6 h-6 mx-auto mb-2" />
                      <span className="text-sm font-medium">网格</span>
                    </div>
                  </button>
                  
                  <button
                    onClick={() => handleChange('layout', 'list')}
                    className={`
                      flex-1 p-3 rounded-lg border-2 transition-all
                      ${localSettings.layout === 'list'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                      }
                    `}
                  >
                    <div className="text-center">
                      <List className="w-6 h-6 mx-auto mb-2" />
                      <span className="text-sm font-medium">列表</span>
                    </div>
                  </button>
                </div>
              </div>

              {/* 网格列数 */}
              {localSettings.layout === 'grid' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    网格列数: {localSettings.columns_count}
                  </label>
                  <input
                    type="range"
                    min="2"
                    max="8"
                    value={localSettings.columns_count}
                    onChange={(e) => handleChange('columns_count', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>2</span>
                    <span>8</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 功能设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <Monitor className="w-5 h-5 mr-2" />
              功能
            </h3>
            
            <div className="space-y-4">
              {/* 显示搜索框 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Eye className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    显示搜索框
                  </span>
                </div>
                <button
                  onClick={() => handleChange('show_search', !localSettings.show_search)}
                  className={`
                    relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                    ${localSettings.show_search ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'}
                  `}
                >
                  <span
                    className={`
                      inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                      ${localSettings.show_search ? 'translate-x-6' : 'translate-x-1'}
                    `}
                  />
                </button>
              </div>

              {/* 显示分类 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Eye className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    显示分类过滤器
                  </span>
                </div>
                <button
                  onClick={() => handleChange('show_categories', !localSettings.show_categories)}
                  className={`
                    relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                    ${localSettings.show_categories ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'}
                  `}
                >
                  <span
                    className={`
                      inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                      ${localSettings.show_categories ? 'translate-x-6' : 'translate-x-1'}
                    `}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* 背景设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <Image className="w-5 h-5 mr-2" />
              背景
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                背景图片 URL（可选）
              </label>
              <input
                type="url"
                value={localSettings.background_image || ''}
                onChange={(e) => handleChange('background_image', e.target.value)}
                placeholder="https://example.com/background.jpg"
                className="
                  w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                  bg-white dark:bg-gray-700
                  text-gray-900 dark:text-white
                  placeholder-gray-500 dark:placeholder-gray-400
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                "
              />
            </div>
          </div>

          {/* 分类管理 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              分类管理
            </h3>
            
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {categories.map((category) => (
                <div
                  key={category.id}
                  className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{category.icon}</span>
                    <span className="text-sm font-medium">{category.name}</span>
                  </div>
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: category.color }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleReset}
            disabled={!hasChanges}
            className="
              px-4 py-2 text-gray-600 dark:text-gray-400
              hover:text-gray-800 dark:hover:text-gray-200
              disabled:opacity-50 disabled:cursor-not-allowed
              transition-colors
            "
          >
            重置
          </button>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="
                px-4 py-2 border border-gray-300 dark:border-gray-600
                text-gray-700 dark:text-gray-300
                rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700
                transition-colors
              "
            >
              取消
            </button>
            <button
              onClick={handleSave}
              disabled={!hasChanges}
              className="
                px-4 py-2 bg-blue-500 text-white rounded-lg
                hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed
                transition-colors
              "
            >
              保存设置
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
