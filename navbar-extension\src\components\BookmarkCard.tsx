import React, { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ExternalLink, Trash2, Edit, Pin, PinOff } from 'lucide-react';
import { Bookmark } from '../types';
import { chromeService } from '../services/chrome';

interface BookmarkCardProps {
  bookmark: Bookmark;
  layout: 'grid' | 'list';
  onDelete: (id: number) => void;
  isDragging?: boolean;
}

const BookmarkCard: React.FC<BookmarkCardProps> = ({
  bookmark,
  layout,
  onDelete,
  isDragging = false,
}) => {
  const [imageError, setImageError] = useState(false);
  const [showActions, setShowActions] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({
    id: bookmark.id.toString(),
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    await chromeService.openTab(bookmark.url);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm(`确定要删除书签 "${bookmark.title}" 吗？`)) {
      onDelete(bookmark.id);
    }
  };

  const getFaviconUrl = () => {
    if (imageError || !bookmark.favicon) {
      return `https://www.google.com/s2/favicons?domain=${new URL(bookmark.url).hostname}&sz=32`;
    }
    return bookmark.favicon;
  };

  if (layout === 'list') {
    return (
      <div
        ref={setNodeRef}
        style={style}
        {...attributes}
        {...listeners}
        className={`
          bookmark-card group
          flex items-center p-4 
          bg-white/80 dark:bg-gray-800/80 
          backdrop-blur-sm rounded-lg 
          border border-gray-200 dark:border-gray-700
          hover:shadow-lg transition-all duration-300
          cursor-pointer
          ${isSortableDragging || isDragging ? 'dragging' : ''}
        `}
        onClick={handleClick}
        onMouseEnter={() => setShowActions(true)}
        onMouseLeave={() => setShowActions(false)}
      >
        {/* Favicon */}
        <div className="flex-shrink-0 mr-4">
          <img
            src={getFaviconUrl()}
            alt=""
            className="w-8 h-8 rounded"
            onError={() => setImageError(true)}
          />
        </div>

        {/* 内容 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white truncate">
              {bookmark.title}
            </h3>
            {bookmark.is_pinned && (
              <Pin className="w-4 h-4 text-yellow-500 flex-shrink-0" />
            )}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
            {bookmark.url}
          </p>
          {bookmark.description && (
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
              {bookmark.description}
            </p>
          )}
        </div>

        {/* 操作按钮 */}
        <div className={`
          flex items-center space-x-2 ml-4
          transition-opacity duration-200
          ${showActions ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}
        `}>
          <button
            onClick={(e) => {
              e.stopPropagation();
              // TODO: 实现编辑功能
            }}
            className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
            title="编辑"
          >
            <Edit className="w-4 h-4" />
          </button>
          
          <button
            onClick={handleDelete}
            className="p-2 text-gray-400 hover:text-red-500 transition-colors"
            title="删除"
          >
            <Trash2 className="w-4 h-4" />
          </button>
          
          <ExternalLink className="w-4 h-4 text-gray-400" />
        </div>
      </div>
    );
  }

  // Grid layout
  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`
        bookmark-card group
        relative p-6 
        bg-white/80 dark:bg-gray-800/80 
        backdrop-blur-sm rounded-xl 
        border border-gray-200 dark:border-gray-700
        hover:shadow-xl transition-all duration-300
        cursor-pointer
        ${isSortableDragging || isDragging ? 'dragging' : ''}
      `}
      onClick={handleClick}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* 置顶标识 */}
      {bookmark.is_pinned && (
        <div className="absolute top-2 right-2">
          <Pin className="w-4 h-4 text-yellow-500" />
        </div>
      )}

      {/* 操作按钮 */}
      <div className={`
        absolute top-2 right-2 flex space-x-1
        transition-opacity duration-200
        ${showActions ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}
        ${bookmark.is_pinned ? 'top-8' : ''}
      `}>
        <button
          onClick={(e) => {
            e.stopPropagation();
            // TODO: 实现编辑功能
          }}
          className="p-1 bg-white/80 dark:bg-gray-700/80 rounded text-gray-400 hover:text-blue-500 transition-colors"
          title="编辑"
        >
          <Edit className="w-3 h-3" />
        </button>
        
        <button
          onClick={handleDelete}
          className="p-1 bg-white/80 dark:bg-gray-700/80 rounded text-gray-400 hover:text-red-500 transition-colors"
          title="删除"
        >
          <Trash2 className="w-3 h-3" />
        </button>
      </div>

      {/* Favicon */}
      <div className="flex justify-center mb-4">
        <img
          src={getFaviconUrl()}
          alt=""
          className="w-12 h-12 rounded-lg shadow-sm"
          onError={() => setImageError(true)}
        />
      </div>

      {/* 标题 */}
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white text-center mb-2 line-clamp-2">
        {bookmark.title}
      </h3>

      {/* URL */}
      <p className="text-sm text-gray-500 dark:text-gray-400 text-center truncate mb-2">
        {new URL(bookmark.url).hostname}
      </p>

      {/* 描述 */}
      {bookmark.description && (
        <p className="text-sm text-gray-600 dark:text-gray-300 text-center line-clamp-3">
          {bookmark.description}
        </p>
      )}

      {/* 外部链接图标 */}
      <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <ExternalLink className="w-4 h-4 text-gray-400" />
      </div>
    </div>
  );
};

export default BookmarkCard;
