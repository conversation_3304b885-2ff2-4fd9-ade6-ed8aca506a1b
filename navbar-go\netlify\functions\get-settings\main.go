// netlify/functions/get-settings/main.go
package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/jackc/pgx/v5"
)

// UserSettings 用户设置结构体
type UserSettings struct {
	ID              int64  `json:"id"`
	Theme           string `json:"theme"`
	Layout          string `json:"layout"`
	ColumnsCount    int    `json:"columns_count"`
	ShowSearch      bool   `json:"show_search"`
	ShowCategories  bool   `json:"show_categories"`
	BackgroundImage string `json:"background_image"`
	CreatedAt       string `json:"created_at"`
	UpdatedAt       string `json:"updated_at"`
}

// APIResponse API 响应结构体
type APIResponse struct {
	Data           *UserSettings `json:"data"`
	ConnectionTime string        `json:"connection_time_ms"`
	QueryTime      string        `json:"query_time_ms"`
}

func handler(request events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
	ctx := context.Background()

	// 处理 CORS 预检请求
	if request.HTTPMethod == "OPTIONS" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 200,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: "",
		}, nil
	}

	// 从环境变量中读取数据库连接字符串
	connString := os.Getenv("SUPABASE_DATABASE_URL")
	if connString == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "SUPABASE_DATABASE_URL not set"}`,
		}, nil
	}

	// 测量数据库连接耗时
	connStartTime := time.Now()
	conn, err := pgx.Connect(ctx, connString)
	connDuration := time.Since(connStartTime)

	if err != nil {
		body := fmt.Sprintf(`{"error": "Unable to connect to database (took %s): %v"}`, connDuration, err)
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: body,
		}, nil
	}
	defer conn.Close(ctx)

	// 测量 SQL 查询执行耗时
	queryStartTime := time.Now()
	
	// 获取第一条设置记录（假设只有一个用户）
	var settings UserSettings
	var createdAt, updatedAt time.Time
	var theme, layout, backgroundImage sql.NullString
	var columnsCount sql.NullInt32
	var showSearch, showCategories sql.NullBool

	query := `SELECT id, theme, layout, columns_count, show_search, show_categories,
			  background_image, created_at, updated_at
			  FROM user_settings ORDER BY id LIMIT 1`

	err = conn.QueryRow(ctx, query).Scan(
		&settings.ID, &theme, &layout, &columnsCount,
		&showSearch, &showCategories, &backgroundImage,
		&createdAt, &updatedAt)
	
	queryDuration := time.Since(queryStartTime)

	if err != nil {
		if err.Error() == "no rows in result set" {
			// 如果没有设置记录，创建默认设置
			return createDefaultSettings(ctx, conn, connDuration, queryDuration)
		}
		
		body := fmt.Sprintf(`{"error": "Query failed (took %s): %v"}`, queryDuration, err)
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: body,
		}, nil
	}

	// 处理可能为 NULL 的字段
	if theme.Valid {
		settings.Theme = theme.String
	} else {
		settings.Theme = "light"
	}

	if layout.Valid {
		settings.Layout = layout.String
	} else {
		settings.Layout = "grid"
	}

	if columnsCount.Valid {
		settings.ColumnsCount = int(columnsCount.Int32)
	} else {
		settings.ColumnsCount = 6
	}

	if showSearch.Valid {
		settings.ShowSearch = showSearch.Bool
	} else {
		settings.ShowSearch = true
	}

	if showCategories.Valid {
		settings.ShowCategories = showCategories.Bool
	} else {
		settings.ShowCategories = true
	}

	if backgroundImage.Valid {
		settings.BackgroundImage = backgroundImage.String
	} else {
		settings.BackgroundImage = ""
	}

	// 格式化时间
	settings.CreatedAt = createdAt.Format(time.RFC3339)
	settings.UpdatedAt = updatedAt.Format(time.RFC3339)

	// 构建响应对象
	responseObject := APIResponse{
		Data:           &settings,
		ConnectionTime: fmt.Sprintf("%.2fms", float64(connDuration.Microseconds())/1000.0),
		QueryTime:      fmt.Sprintf("%.2fms", float64(queryDuration.Microseconds())/1000.0),
	}

	// 将响应对象序列化为 JSON
	responseBody, err := json.Marshal(responseObject)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	// 返回成功的响应
	return &events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func createDefaultSettings(ctx context.Context, conn *pgx.Conn, connDuration, queryDuration time.Duration) (*events.APIGatewayProxyResponse, error) {
	// 创建默认设置
	var settings UserSettings
	var createdAt, updatedAt time.Time
	var theme, layout, backgroundImage sql.NullString
	var columnsCount sql.NullInt32
	var showSearch, showCategories sql.NullBool

	insertQuery := `INSERT INTO user_settings (theme, layout, columns_count, show_search, show_categories)
					VALUES ('light', 'grid', 6, true, true)
					RETURNING id, theme, layout, columns_count, show_search, show_categories,
					background_image, created_at, updated_at`

	err := conn.QueryRow(ctx, insertQuery).Scan(
		&settings.ID, &theme, &layout, &columnsCount,
		&showSearch, &showCategories, &backgroundImage,
		&createdAt, &updatedAt)
	
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to create default settings: %v"}`, err),
		}, nil
	}

	// 处理可能为 NULL 的字段
	if theme.Valid {
		settings.Theme = theme.String
	} else {
		settings.Theme = "light"
	}

	if layout.Valid {
		settings.Layout = layout.String
	} else {
		settings.Layout = "grid"
	}

	if columnsCount.Valid {
		settings.ColumnsCount = int(columnsCount.Int32)
	} else {
		settings.ColumnsCount = 6
	}

	if showSearch.Valid {
		settings.ShowSearch = showSearch.Bool
	} else {
		settings.ShowSearch = true
	}

	if showCategories.Valid {
		settings.ShowCategories = showCategories.Bool
	} else {
		settings.ShowCategories = true
	}

	if backgroundImage.Valid {
		settings.BackgroundImage = backgroundImage.String
	} else {
		settings.BackgroundImage = ""
	}

	// 格式化时间
	settings.CreatedAt = createdAt.Format(time.RFC3339)
	settings.UpdatedAt = updatedAt.Format(time.RFC3339)

	// 构建响应对象
	responseObject := APIResponse{
		Data:           &settings,
		ConnectionTime: fmt.Sprintf("%.2fms", float64(connDuration.Microseconds())/1000.0),
		QueryTime:      fmt.Sprintf("%.2fms", float64(queryDuration.Microseconds())/1000.0),
	}

	// 将响应对象序列化为 JSON
	responseBody, err := json.Marshal(responseObject)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	return &events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func main() {
	lambda.Start(handler)
}
