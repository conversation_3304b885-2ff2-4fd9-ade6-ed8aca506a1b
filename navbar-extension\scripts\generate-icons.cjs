// 简单的图标生成脚本
// 在实际项目中，你可能需要使用更专业的工具如 sharp 或 imagemagick

const fs = require('fs');
const path = require('path');

// 创建简单的 base64 编码的 PNG 图标
const createIcon = (size) => {
  // 这是一个简单的蓝色方形图标的 base64 编码
  // 在实际项目中，你应该使用真正的图标设计
  const canvas = `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="url(#gradient)"/>
      <path d="M${size * 0.25} ${size * 0.375}h${size * 0.5}v${size * 0.0625}H${size * 0.25}v-${size * 0.0625}zm0 ${size * 0.125}h${size * 0.5}v${size * 0.0625}H${size * 0.25}v-${size * 0.0625}zm0 ${size * 0.125}h${size * 0.375}v${size * 0.0625}H${size * 0.25}v-${size * 0.0625}z" fill="white"/>
      <circle cx="${size * 0.6875}" cy="${size * 0.625}" r="${size * 0.0625}" fill="white"/>
      <defs>
        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#667eea"/>
          <stop offset="100%" style="stop-color:#764ba2"/>
        </linearGradient>
      </defs>
    </svg>
  `;
  
  return canvas;
};

// 创建图标目录
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// 生成不同尺寸的图标
const sizes = [16, 32, 48, 128];

sizes.forEach(size => {
  const svgContent = createIcon(size);
  const filename = `icon-${size}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`Generated ${filename}`);
});

console.log('Icons generated successfully!');
console.log('Note: These are SVG placeholders. For production, convert them to PNG files.');
console.log('You can use online tools or libraries like sharp to convert SVG to PNG.');
