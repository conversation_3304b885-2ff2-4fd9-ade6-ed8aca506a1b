chrome.runtime.onMessage.addListener((e,o,t)=>{switch(console.log("Content script received message:",e),e.action){case"bookmarkAdded":r("书签添加成功！","success");break;case"bookmarkDeleted":r("书签删除成功！","info");break;case"bookmarkError":r("操作失败："+e.error,"error");break;case"getPageInfo":t({title:document.title,url:window.location.href,description:c(),favicon:s()});break;default:t({received:!0})}});function c(){const e=document.querySelector('meta[name="description"]');if(e&&e.content)return e.content.trim();const o=document.querySelector('meta[property="og:description"]');if(o&&o.content)return o.content.trim();const t=document.querySelector("p");if(t&&t.textContent){const n=t.textContent.trim();return n.length>200?n.substring(0,200)+"...":n}return""}function s(){const e=document.querySelector('link[rel*="icon"]');return e&&e.href?e.href:`${window.location.protocol}//${window.location.hostname}/favicon.ico`}function r(e,o="info"){let t=document.getElementById("navbar-extension-notifications");t||(t=document.createElement("div"),t.id="navbar-extension-notifications",t.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      pointer-events: none;
    `,document.body.appendChild(t));const n=document.createElement("div");n.style.cssText=`
    margin-bottom: 10px;
    padding: 12px 16px;
    border-radius: 8px;
    color: white;
    font-family: system-ui, -apple-system, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.3s ease;
    transform: translateX(100%);
    opacity: 0;
    max-width: 300px;
    word-wrap: break-word;
    background: ${d(o)};
  `,n.textContent=e,t.appendChild(n),requestAnimationFrame(()=>{n.style.transform="translateX(0)",n.style.opacity="1"}),n.addEventListener("click",()=>{i(n)}),setTimeout(()=>{i(n)},5e3)}function d(e){switch(e){case"success":return"linear-gradient(135deg, #10b981, #059669)";case"error":return"linear-gradient(135deg, #ef4444, #dc2626)";case"info":default:return"linear-gradient(135deg, #3b82f6, #2563eb)"}}function i(e){e.style.transform="translateX(100%)",e.style.opacity="0",setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e)},300)}document.addEventListener("keydown",e=>{(e.ctrlKey||e.metaKey)&&e.shiftKey&&e.key==="B"&&(e.preventDefault(),chrome.runtime.sendMessage({action:"addBookmark",data:{title:document.title,url:window.location.href,description:c(),favicon:s()}})),(e.ctrlKey||e.metaKey)&&e.shiftKey&&e.key==="N"&&(e.preventDefault(),chrome.runtime.sendMessage({action:"openNavigation"}))});document.readyState==="loading"?document.addEventListener("DOMContentLoaded",a):a();function a(){console.log("Navbar extension content script initialized")}
