#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\Project\ai\navbar\navbar-extension\node_modules\.pnpm\cssesc@3.0.0\node_modules\cssesc\bin\node_modules;D:\Project\ai\navbar\navbar-extension\node_modules\.pnpm\cssesc@3.0.0\node_modules\cssesc\node_modules;D:\Project\ai\navbar\navbar-extension\node_modules\.pnpm\cssesc@3.0.0\node_modules;D:\Project\ai\navbar\navbar-extension\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/proc/cygdrive/d/Project/ai/navbar/navbar-extension/node_modules/.pnpm/cssesc@3.0.0/node_modules/cssesc/bin/node_modules:/proc/cygdrive/d/Project/ai/navbar/navbar-extension/node_modules/.pnpm/cssesc@3.0.0/node_modules/cssesc/node_modules:/proc/cygdrive/d/Project/ai/navbar/navbar-extension/node_modules/.pnpm/cssesc@3.0.0/node_modules:/proc/cygdrive/d/Project/ai/navbar/navbar-extension/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../cssesc/bin/cssesc" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../cssesc/bin/cssesc" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../cssesc/bin/cssesc" $args
  } else {
    & "node$exe"  "$basedir/../cssesc/bin/cssesc" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
