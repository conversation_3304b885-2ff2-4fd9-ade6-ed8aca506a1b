const s="https://your-netlify-site.netlify.app/.netlify/functions";chrome.runtime.onInstalled.addListener(e=>{console.log("Extension installed:",e),chrome.contextMenus.create({id:"add-to-navbar",title:"添加到个人导航",contexts:["page","link"]})});chrome.contextMenus.onClicked.addListener(async(e,t)=>{if(e.menuItemId==="add-to-navbar"){const r=e.linkUrl||(t==null?void 0:t.url),o=e.selectionText||(t==null?void 0:t.title);r&&o&&await c({title:o,url:r,description:""})}});chrome.runtime.onMessage.addListener((e,t,r)=>{switch(console.log("Background received message:",e),e.action){case"addBookmark":return n(e.data).then(o=>r({success:!0,data:o})).catch(o=>r({success:!1,error:o.message})),!0;case"getBookmarks":return i().then(o=>r({success:!0,data:o})).catch(o=>r({success:!1,error:o.message})),!0;case"deleteBookmark":return d(e.id).then(o=>r({success:!0,data:o})).catch(o=>r({success:!1,error:o.message})),!0;default:r({success:!1,error:"Unknown action"})}});async function c(e){try{const t=await fetch(`${s}/add-bookmark`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.json();return chrome.tabs.query({},o=>{o.forEach(a=>{a.id&&chrome.tabs.sendMessage(a.id,{action:"bookmarkAdded",data:r.data}).catch(()=>{})})}),r}catch(t){throw console.error("Failed to add bookmark:",t),t}}async function n(e){return await c(e)}async function i(){try{const e=await fetch(`${s}/get-bookmarks`);if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);return await e.json()}catch(e){throw console.error("Failed to get bookmarks:",e),e}}async function d(e){try{const t=await fetch(`${s}/delete-bookmark?id=${e}`,{method:"DELETE"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.json();return chrome.tabs.query({},o=>{o.forEach(a=>{a.id&&chrome.tabs.sendMessage(a.id,{action:"bookmarkDeleted",id:e}).catch(()=>{})})}),r}catch(t){throw console.error("Failed to delete bookmark:",t),t}}chrome.tabs.onUpdated.addListener((e,t,r)=>{t.status==="complete"&&r.url&&(r.url==="chrome://newtab/"||r.url==="edge://newtab/")&&chrome.tabs.update(e,{url:chrome.runtime.getURL("index.html")})});chrome.action.onClicked.addListener(e=>{chrome.tabs.create({url:chrome.runtime.getURL("index.html")})});setInterval(async()=>{},5*60*1e3);
