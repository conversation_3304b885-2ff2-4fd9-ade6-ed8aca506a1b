{"name": "util-deprecate", "version": "1.0.2", "description": "The Node.js `util.deprecate()` function with browser support", "main": "node.js", "browser": "browser.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/util-deprecate.git"}, "keywords": ["util", "deprecate", "browserify", "browser", "node"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/util-deprecate/issues"}, "homepage": "https://github.com/TooTallNate/util-deprecate"}