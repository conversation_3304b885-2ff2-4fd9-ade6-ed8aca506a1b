@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: Inter, system-ui, sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 暗色主题滚动条 */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* 书签卡片悬停效果 */
.bookmark-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bookmark-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 分类标签动画 */
.category-tag {
  transition: all 0.2s ease-in-out;
}

.category-tag:hover {
  transform: scale(1.05);
}

/* 搜索框聚焦效果 */
.search-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 加载动画 */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 拖拽时的样式 */
.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.drag-overlay {
  transform: rotate(5deg);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 毛玻璃效果 */
.glass {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 自定义网格布局 */
.custom-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(var(--grid-cols, 6), minmax(0, 1fr));
}

/* 响应式网格 */
@media (max-width: 640px) {
  .custom-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .custom-grid {
    grid-template-columns: repeat(min(var(--grid-cols, 6), 3), minmax(0, 1fr));
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .custom-grid {
    grid-template-columns: repeat(min(var(--grid-cols, 6), 4), minmax(0, 1fr));
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .custom-grid {
    grid-template-columns: repeat(min(var(--grid-cols, 6), 5), minmax(0, 1fr));
  }
}

@media (min-width: 1281px) {
  .custom-grid {
    grid-template-columns: repeat(var(--grid-cols, 6), minmax(0, 1fr));
  }
}
