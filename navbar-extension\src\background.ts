// background.ts - Chrome 扩展背景脚本

// API 基础 URL - 需要替换为实际的 Netlify 部署 URL
const API_BASE_URL = 'https://your-netlify-site.netlify.app/.netlify/functions';

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Extension installed:', details);
  
  // 创建右键菜单
  chrome.contextMenus.create({
    id: 'add-to-navbar',
    title: '添加到个人导航',
    contexts: ['page', 'link'],
  });
});

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  if (info.menuItemId === 'add-to-navbar') {
    const url = info.linkUrl || tab?.url;
    const title = info.selectionText || tab?.title;
    
    if (url && title) {
      await addBookmarkToAPI({
        title,
        url,
        description: '',
      });
    }
  }
});

// 处理来自 popup 和 content script 的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message);
  
  switch (message.action) {
    case 'addBookmark':
      handleAddBookmark(message.data)
        .then((result) => sendResponse({ success: true, data: result }))
        .catch((error) => sendResponse({ success: false, error: error.message }));
      return true; // 保持消息通道开放
      
    case 'getBookmarks':
      handleGetBookmarks()
        .then((result) => sendResponse({ success: true, data: result }))
        .catch((error) => sendResponse({ success: false, error: error.message }));
      return true;
      
    case 'deleteBookmark':
      handleDeleteBookmark(message.id)
        .then((result) => sendResponse({ success: true, data: result }))
        .catch((error) => sendResponse({ success: false, error: error.message }));
      return true;
      
    default:
      sendResponse({ success: false, error: 'Unknown action' });
  }
});

// 添加书签到 API
async function addBookmarkToAPI(bookmarkData: any) {
  try {
    const response = await fetch(`${API_BASE_URL}/add-bookmark`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(bookmarkData),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    // 通知所有标签页书签已添加
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach((tab) => {
        if (tab.id) {
          chrome.tabs.sendMessage(tab.id, {
            action: 'bookmarkAdded',
            data: result.data,
          }).catch(() => {
            // 忽略无法发送消息的标签页
          });
        }
      });
    });
    
    return result;
  } catch (error) {
    console.error('Failed to add bookmark:', error);
    throw error;
  }
}

// 处理添加书签请求
async function handleAddBookmark(bookmarkData: any) {
  return await addBookmarkToAPI(bookmarkData);
}

// 处理获取书签请求
async function handleGetBookmarks() {
  try {
    const response = await fetch(`${API_BASE_URL}/get-bookmarks`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to get bookmarks:', error);
    throw error;
  }
}

// 处理删除书签请求
async function handleDeleteBookmark(bookmarkId: number) {
  try {
    const response = await fetch(`${API_BASE_URL}/delete-bookmark?id=${bookmarkId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    // 通知所有标签页书签已删除
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach((tab) => {
        if (tab.id) {
          chrome.tabs.sendMessage(tab.id, {
            action: 'bookmarkDeleted',
            id: bookmarkId,
          }).catch(() => {
            // 忽略无法发送消息的标签页
          });
        }
      });
    });
    
    return result;
  } catch (error) {
    console.error('Failed to delete bookmark:', error);
    throw error;
  }
}

// 监听标签页更新，用于检测新标签页
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // 当标签页完成加载时
  if (changeInfo.status === 'complete' && tab.url) {
    // 检查是否是新标签页
    if (tab.url === 'chrome://newtab/' || tab.url === 'edge://newtab/') {
      // 重定向到我们的导航页面
      chrome.tabs.update(tabId, {
        url: chrome.runtime.getURL('index.html'),
      });
    }
  }
});

// 处理扩展图标点击（如果没有 popup）
chrome.action.onClicked.addListener((tab) => {
  // 打开导航页面
  chrome.tabs.create({
    url: chrome.runtime.getURL('index.html'),
  });
});

// 定期同步数据（可选）
setInterval(async () => {
  try {
    // 可以在这里实现定期同步逻辑
    // 例如：检查服务器上的书签更新
  } catch (error) {
    console.error('Sync error:', error);
  }
}, 5 * 60 * 1000); // 每5分钟同步一次

export {};
