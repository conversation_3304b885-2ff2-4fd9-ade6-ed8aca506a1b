<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网格列数测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .slider-container {
            margin: 10px 0;
        }
        
        .slider {
            width: 200px;
            margin: 0 10px;
        }
        
        /* 自定义网格布局 */
        .custom-grid {
            display: grid;
            gap: 1.5rem;
            grid-template-columns: repeat(var(--grid-cols, 6), minmax(0, 1fr));
        }

        /* 响应式网格 */
        @media (max-width: 640px) {
            .custom-grid {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }

        @media (min-width: 641px) and (max-width: 768px) {
            .custom-grid {
                grid-template-columns: repeat(min(var(--grid-cols, 6), 3), minmax(0, 1fr));
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .custom-grid {
                grid-template-columns: repeat(min(var(--grid-cols, 6), 4), minmax(0, 1fr));
            }
        }

        @media (min-width: 1025px) and (max-width: 1280px) {
            .custom-grid {
                grid-template-columns: repeat(min(var(--grid-cols, 6), 5), minmax(0, 1fr));
            }
        }

        @media (min-width: 1281px) {
            .custom-grid {
                grid-template-columns: repeat(var(--grid-cols, 6), minmax(0, 1fr));
            }
        }
        
        .test-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            font-weight: bold;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <h1>网格列数测试</h1>
    
    <div class="test-section">
        <h2>网格列数控制</h2>
        <div class="controls">
            <div class="slider-container">
                <label for="columns">列数: <span id="columns-value">6</span></label>
                <input type="range" id="columns" class="slider" min="2" max="8" value="6">
            </div>
            <div class="info">
                <strong>响应式说明:</strong>
                <ul>
                    <li>手机 (≤640px): 固定2列</li>
                    <li>平板 (641-768px): 最多3列</li>
                    <li>小桌面 (769-1024px): 最多4列</li>
                    <li>中桌面 (1025-1280px): 最多5列</li>
                    <li>大桌面 (≥1281px): 使用设置的列数</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>网格预览</h2>
        <div id="grid-container" class="custom-grid">
            <!-- 卡片将通过 JavaScript 生成 -->
        </div>
    </div>

    <div class="test-section">
        <h2>当前屏幕信息</h2>
        <div id="screen-info"></div>
    </div>

    <script>
        const columnsSlider = document.getElementById('columns');
        const columnsValue = document.getElementById('columns-value');
        const gridContainer = document.getElementById('grid-container');
        const screenInfo = document.getElementById('screen-info');

        function updateGrid() {
            const columns = parseInt(columnsSlider.value);
            columnsValue.textContent = columns;
            
            // 设置 CSS 变量
            gridContainer.style.setProperty('--grid-cols', columns);
            
            // 生成测试卡片
            gridContainer.innerHTML = '';
            for (let i = 1; i <= 16; i++) {
                const card = document.createElement('div');
                card.className = 'test-card';
                card.textContent = `卡片 ${i}`;
                gridContainer.appendChild(card);
            }
        }

        function updateScreenInfo() {
            const width = window.innerWidth;
            let breakpoint = '';
            
            if (width <= 640) {
                breakpoint = '手机 (≤640px)';
            } else if (width <= 768) {
                breakpoint = '平板 (641-768px)';
            } else if (width <= 1024) {
                breakpoint = '小桌面 (769-1024px)';
            } else if (width <= 1280) {
                breakpoint = '中桌面 (1025-1280px)';
            } else {
                breakpoint = '大桌面 (≥1281px)';
            }
            
            screenInfo.innerHTML = `
                <p><strong>屏幕宽度:</strong> ${width}px</p>
                <p><strong>当前断点:</strong> ${breakpoint}</p>
                <p><strong>设置列数:</strong> ${columnsSlider.value}</p>
                <p><strong>实际显示列数:</strong> 请观察上方网格</p>
            `;
        }

        // 事件监听
        columnsSlider.addEventListener('input', updateGrid);
        window.addEventListener('resize', updateScreenInfo);

        // 初始化
        updateGrid();
        updateScreenInfo();
    </script>
</body>
</html>
