package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	_ "github.com/lib/pq"
)

// BatchUpdateSortOrderRequest 批量更新排序请求
type BatchUpdateSortOrderRequest struct {
	Updates []SortOrderUpdate `json:"updates"`
}

// SortOrderUpdate 单个排序更新
type SortOrderUpdate struct {
	ID        int `json:"id"`
	SortOrder int `json:"sort_order"`
}

// BatchUpdateSortOrderResponse 批量更新排序响应
type BatchUpdateSortOrderResponse struct {
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	UpdatedCount int    `json:"updated_count"`
}

// APIResponse 通用API响应格式
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// 设置CORS头
	headers := map[string]string{
		"Access-Control-Allow-Origin":  "*",
		"Access-Control-Allow-Headers": "Content-Type",
		"Access-Control-Allow-Methods": "PUT, OPTIONS",
		"Content-Type":                 "application/json",
	}

	// 处理OPTIONS请求
	if request.HTTPMethod == "OPTIONS" {
		return events.APIGatewayProxyResponse{
			StatusCode: 200,
			Headers:    headers,
			Body:       "",
		}, nil
	}

	// 只允许PUT方法
	if request.HTTPMethod != "PUT" {
		response := APIResponse{
			Success: false,
			Message: "Method not allowed",
		}
		body, _ := json.Marshal(response)
		return events.APIGatewayProxyResponse{
			StatusCode: 405,
			Headers:    headers,
			Body:       string(body),
		}, nil
	}

	// 解析请求体
	var req BatchUpdateSortOrderRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("Failed to parse request body: %v", err)
		response := APIResponse{
			Success: false,
			Message: "Invalid request body",
		}
		body, _ := json.Marshal(response)
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers:    headers,
			Body:       string(body),
		}, nil
	}

	// 验证请求
	if len(req.Updates) == 0 {
		response := APIResponse{
			Success: false,
			Message: "No updates provided",
		}
		body, _ := json.Marshal(response)
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers:    headers,
			Body:       string(body),
		}, nil
	}

	// 连接数据库
	db, err := connectDB()
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		response := APIResponse{
			Success: false,
			Message: "Database connection failed",
		}
		body, _ := json.Marshal(response)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers:    headers,
			Body:       string(body),
		}, nil
	}
	defer db.Close()

	// 批量更新排序
	updatedCount, err := batchUpdateSortOrder(db, req.Updates)
	if err != nil {
		log.Printf("Failed to batch update sort order: %v", err)
		response := APIResponse{
			Success: false,
			Message: "Failed to update sort order",
		}
		body, _ := json.Marshal(response)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers:    headers,
			Body:       string(body),
		}, nil
	}

	// 返回成功响应
	responseData := BatchUpdateSortOrderResponse{
		Success:      true,
		Message:      "Sort order updated successfully",
		UpdatedCount: updatedCount,
	}

	response := APIResponse{
		Success: true,
		Message: "Sort order updated successfully",
		Data:    responseData,
	}

	body, err := json.Marshal(response)
	if err != nil {
		log.Printf("Failed to marshal response: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers:    headers,
			Body:       `{"success":false,"message":"Internal server error"}`,
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers:    headers,
		Body:       string(body),
	}, nil
}

// connectDB 连接数据库
func connectDB() (*sql.DB, error) {
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		return nil, fmt.Errorf("DATABASE_URL environment variable not set")
	}

	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		return nil, err
	}

	if err := db.Ping(); err != nil {
		return nil, err
	}

	return db, nil
}

// batchUpdateSortOrder 批量更新书签排序
func batchUpdateSortOrder(db *sql.DB, updates []SortOrderUpdate) (int, error) {
	if len(updates) == 0 {
		return 0, nil
	}

	// 开始事务
	tx, err := db.Begin()
	if err != nil {
		return 0, fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 构建批量更新SQL
	// 使用CASE WHEN语句进行批量更新
	var caseWhenParts []string
	var ids []string
	var args []interface{}
	argIndex := 1

	for _, update := range updates {
		caseWhenParts = append(caseWhenParts, fmt.Sprintf("WHEN id = $%d THEN $%d", argIndex, argIndex+1))
		ids = append(ids, fmt.Sprintf("$%d", argIndex))
		args = append(args, update.ID, update.SortOrder)
		argIndex += 2
	}

	// 构建完整的SQL语句
	sql := fmt.Sprintf(`
		UPDATE bookmarks 
		SET sort_order = CASE %s END,
		    updated_at = CURRENT_TIMESTAMP
		WHERE id IN (%s)
	`, strings.Join(caseWhenParts, " "), strings.Join(ids, ","))

	log.Printf("Executing batch update SQL: %s", sql)
	log.Printf("With args: %v", args)

	// 执行更新
	result, err := tx.Exec(sql, args...)
	if err != nil {
		return 0, fmt.Errorf("failed to execute batch update: %v", err)
	}

	// 获取受影响的行数
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("failed to get rows affected: %v", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return 0, fmt.Errorf("failed to commit transaction: %v", err)
	}

	log.Printf("Successfully updated %d bookmarks", rowsAffected)
	return int(rowsAffected), nil
}

func main() {
	lambda.Start(handler)
}
