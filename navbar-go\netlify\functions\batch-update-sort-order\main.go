package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/jackc/pgx/v5"
)

// BatchUpdateSortOrderRequest 批量更新排序请求
type BatchUpdateSortOrderRequest struct {
	Updates []SortOrderUpdate `json:"updates"`
}

// SortOrderUpdate 单个排序更新
type SortOrderUpdate struct {
	ID        int `json:"id"`
	SortOrder int `json:"sort_order"`
}

// BatchUpdateSortOrderResponse 批量更新排序响应
type BatchUpdateSortOrderResponse struct {
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	UpdatedCount int    `json:"updated_count"`
}

// APIResponse 通用API响应格式
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// 设置CORS头
	headers := map[string]string{
		"Access-Control-Allow-Origin":  "*",
		"Access-Control-Allow-Headers": "Content-Type",
		"Access-Control-Allow-Methods": "PUT, OPTIONS",
		"Content-Type":                 "application/json",
	}

	// 处理OPTIONS请求
	if request.HTTPMethod == "OPTIONS" {
		return events.APIGatewayProxyResponse{
			StatusCode: 200,
			Headers:    headers,
			Body:       "",
		}, nil
	}

	// 只允许PUT方法
	if request.HTTPMethod != "PUT" {
		response := APIResponse{
			Success: false,
			Message: "Method not allowed",
		}
		body, _ := json.Marshal(response)
		return events.APIGatewayProxyResponse{
			StatusCode: 405,
			Headers:    headers,
			Body:       string(body),
		}, nil
	}

	// 解析请求体
	var req BatchUpdateSortOrderRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("Failed to parse request body: %v", err)
		response := APIResponse{
			Success: false,
			Message: "Invalid request body",
		}
		body, _ := json.Marshal(response)
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers:    headers,
			Body:       string(body),
		}, nil
	}

	// 验证请求
	if len(req.Updates) == 0 {
		response := APIResponse{
			Success: false,
			Message: "No updates provided",
		}
		body, _ := json.Marshal(response)
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers:    headers,
			Body:       string(body),
		}, nil
	}

	// 连接数据库
	conn, err := connectDB(ctx)
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		response := APIResponse{
			Success: false,
			Message: "Database connection failed",
		}
		body, _ := json.Marshal(response)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers:    headers,
			Body:       string(body),
		}, nil
	}
	defer conn.Close(ctx)

	// 批量更新排序
	updatedCount, err := batchUpdateSortOrder(ctx, conn, req.Updates)
	if err != nil {
		log.Printf("Failed to batch update sort order: %v", err)
		response := APIResponse{
			Success: false,
			Message: "Failed to update sort order",
		}
		body, _ := json.Marshal(response)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers:    headers,
			Body:       string(body),
		}, nil
	}

	// 返回成功响应
	responseData := BatchUpdateSortOrderResponse{
		Success:      true,
		Message:      "Sort order updated successfully",
		UpdatedCount: updatedCount,
	}

	response := APIResponse{
		Success: true,
		Message: "Sort order updated successfully",
		Data:    responseData,
	}

	body, err := json.Marshal(response)
	if err != nil {
		log.Printf("Failed to marshal response: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers:    headers,
			Body:       `{"success":false,"message":"Internal server error"}`,
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers:    headers,
		Body:       string(body),
	}, nil
}

// connectDB 连接数据库
func connectDB(ctx context.Context) (*pgx.Conn, error) {
	dbURL := os.Getenv("SUPABASE_DATABASE_URL")
	if dbURL == "" {
		return nil, fmt.Errorf("SUPABASE_DATABASE_URL environment variable not set")
	}

	conn, err := pgx.Connect(ctx, dbURL)
	if err != nil {
		return nil, err
	}

	return conn, nil
}

// batchUpdateSortOrder 批量更新书签排序 (优化并修复类型问题版)
func batchUpdateSortOrder(ctx context.Context, conn *pgx.Conn, updates []SortOrderUpdate) (int, error) {
	if len(updates) == 0 {
		return 0, nil
	}

	log.Printf("Starting optimized batch update for %d bookmarks", len(updates))

	sqlStart := `
		UPDATE bookmarks AS b
		SET
			sort_order = u.sort_order,
			updated_at = CURRENT_TIMESTAMP
		FROM (VALUES
	`
	var valueStrings []string
	var valueArgs []interface{}

	// 循环遍历所有更新，构建占位符和参数列表
	for i, update := range updates {
		placeholder1 := i*2 + 1
		placeholder2 := i*2 + 2

		// 为每个占位符显式添加类型转换，以避免类型推断错误
		// 假设 bookmarks.id 是 bigint, bookmarks.sort_order 是 integer
		valueStrings = append(valueStrings, fmt.Sprintf("($%d::bigint, $%d::integer)", placeholder1, placeholder2))

		valueArgs = append(valueArgs, update.ID, update.SortOrder)
	}

	sqlEnd := `
		) AS u(id, sort_order)
		WHERE b.id = u.id;
	`
	finalSQL := sqlStart + strings.Join(valueStrings, ", ") + sqlEnd

	log.Printf("Generated SQL: %s", finalSQL)
	log.Printf("With args: %v", valueArgs)

	tx, err := conn.Begin(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback(ctx)

	log.Printf("Executing single batch UPDATE statement for %d items", len(updates))
	commandTag, err := tx.Exec(ctx, finalSQL, valueArgs...)
	if err != nil {
		log.Printf("Batch update statement failed: %v", err)
		return 0, fmt.Errorf("batch update statement failed: %v", err)
	}

	if err := tx.Commit(ctx); err != nil {
		return 0, fmt.Errorf("failed to commit transaction: %v", err)
	}

	updatedCount := int(commandTag.RowsAffected())
	log.Printf("Successfully updated %d bookmarks in a single batch", updatedCount)

	return updatedCount, nil
}

func main() {
	lambda.Start(handler)
}