// netlify/functions/add-bookmark/main.go
package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/jackc/pgx/v5"
)

// AddBookmarkRequest 添加书签请求结构体
type AddBookmarkRequest struct {
	Title       string `json:"title"`
	URL         string `json:"url"`
	Description string `json:"description"`
	CategoryID  *int64 `json:"category_id"`
	Favicon     string `json:"favicon"`
}

// Bookmark 书签结构体
type Bookmark struct {
	ID          int64  `json:"id"`
	Title       string `json:"title"`
	URL         string `json:"url"`
	Description string `json:"description"`
	Favicon     string `json:"favicon"`
	CategoryID  *int64 `json:"category_id"`
	SortOrder   int    `json:"sort_order"`
	IsPinned    bool   `json:"is_pinned"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// APIResponse API 响应结构体
type APIResponse struct {
	Data    *Bookmark `json:"data"`
	Message string    `json:"message"`
}

func handler(request events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
	ctx := context.Background()

	// 处理 CORS 预检请求
	if request.HTTPMethod == "OPTIONS" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 200,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: "",
		}, nil
	}

	// 只允许 POST 请求
	if request.HTTPMethod != "POST" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 405,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Method not allowed"}`,
		}, nil
	}

	// 从环境变量中读取数据库连接字符串
	connString := os.Getenv("SUPABASE_DATABASE_URL")
	if connString == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "SUPABASE_DATABASE_URL not set"}`,
		}, nil
	}

	// 解析请求体
	var req AddBookmarkRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Invalid JSON: %v"}`, err),
		}, nil
	}

	// 验证必填字段
	if strings.TrimSpace(req.Title) == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Title is required"}`,
		}, nil
	}

	if strings.TrimSpace(req.URL) == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "URL is required"}`,
		}, nil
	}

	// 连接数据库
	conn, err := pgx.Connect(ctx, connString)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Unable to connect to database: %v"}`, err),
		}, nil
	}
	defer conn.Close(ctx)

	// 如果没有提供 favicon，尝试生成默认的
	if req.Favicon == "" {
		// 从 URL 中提取域名并生成 favicon URL
		if strings.HasPrefix(req.URL, "http") {
			parts := strings.Split(req.URL, "/")
			if len(parts) >= 3 {
				domain := parts[2]
				req.Favicon = fmt.Sprintf("https://%s/favicon.ico", domain)
			}
		}
	}

	// 获取当前最大的 sort_order
	var maxSortOrder int
	err = conn.QueryRow(ctx, "SELECT COALESCE(MAX(sort_order), 0) FROM bookmarks").Scan(&maxSortOrder)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to get max sort order: %v"}`, err),
		}, nil
	}

	// 插入新书签
	var newBookmark Bookmark
	var createdAt, updatedAt time.Time
	var description, favicon sql.NullString

	query := `INSERT INTO bookmarks (title, url, description, favicon, category_id, sort_order, is_pinned)
			  VALUES ($1, $2, $3, $4, $5, $6, $7)
			  RETURNING id, title, url, description, favicon, category_id, sort_order, is_pinned, created_at, updated_at`

	err = conn.QueryRow(ctx, query, req.Title, req.URL, req.Description, req.Favicon,
		req.CategoryID, maxSortOrder+1, false).Scan(
		&newBookmark.ID, &newBookmark.Title, &newBookmark.URL, &description,
		&favicon, &newBookmark.CategoryID, &newBookmark.SortOrder,
		&newBookmark.IsPinned, &createdAt, &updatedAt)
	
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to insert bookmark: %v"}`, err),
		}, nil
	}

	// 处理可能为 NULL 的字段
	if description.Valid {
		newBookmark.Description = description.String
	} else {
		newBookmark.Description = ""
	}

	if favicon.Valid {
		newBookmark.Favicon = favicon.String
	} else {
		newBookmark.Favicon = ""
	}

	// 格式化时间
	newBookmark.CreatedAt = createdAt.Format(time.RFC3339)
	newBookmark.UpdatedAt = updatedAt.Format(time.RFC3339)

	// 构建响应
	response := APIResponse{
		Data:    &newBookmark,
		Message: "Bookmark added successfully",
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	return &events.APIGatewayProxyResponse{
		StatusCode: 201,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func main() {
	lambda.Start(handler)
}
