// netlify/functions/manage-categories/main.go
package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/jackc/pgx/v5"
)

// Category 分类结构体
type Category struct {
	ID        int64  `json:"id"`
	Name      string `json:"name"`
	Icon      string `json:"icon"`
	Color     string `json:"color"`
	SortOrder int    `json:"sort_order"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}

// CreateCategoryRequest 创建分类请求
type CreateCategoryRequest struct {
	Name  string `json:"name"`
	Icon  string `json:"icon"`
	Color string `json:"color"`
}

// UpdateCategoryRequest 更新分类请求
type UpdateCategoryRequest struct {
	Name      *string `json:"name"`
	Icon      *string `json:"icon"`
	Color     *string `json:"color"`
	SortOrder *int    `json:"sort_order"`
}

// APIResponse API 响应结构体
type APIResponse struct {
	Data    *Category `json:"data,omitempty"`
	Message string    `json:"message"`
	ID      *int64    `json:"id,omitempty"`
}

func handler(request events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
	ctx := context.Background()

	// 处理 CORS 预检请求
	if request.HTTPMethod == "OPTIONS" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 200,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: "",
		}, nil
	}

	// 从环境变量中读取数据库连接字符串
	connString := os.Getenv("SUPABASE_DATABASE_URL")
	if connString == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "SUPABASE_DATABASE_URL not set"}`,
		}, nil
	}

	// 连接数据库
	conn, err := pgx.Connect(ctx, connString)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Unable to connect to database: %v"}`, err),
		}, nil
	}
	defer conn.Close(ctx)

	// 根据 HTTP 方法处理不同操作
	switch request.HTTPMethod {
	case "POST":
		return handleCreateCategory(ctx, conn, request)
	case "PUT":
		return handleUpdateCategory(ctx, conn, request)
	case "DELETE":
		return handleDeleteCategory(ctx, conn, request)
	default:
		return &events.APIGatewayProxyResponse{
			StatusCode: 405,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Method not allowed"}`,
		}, nil
	}
}

func handleCreateCategory(ctx context.Context, conn *pgx.Conn, request events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
	// 解析请求体
	var req CreateCategoryRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Invalid JSON: %v"}`, err),
		}, nil
	}

	// 验证必填字段
	if strings.TrimSpace(req.Name) == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Category name is required"}`,
		}, nil
	}

	// 设置默认值
	if req.Icon == "" {
		req.Icon = "📁"
	}
	if req.Color == "" {
		req.Color = "#3B82F6"
	}

	// 获取当前最大的 sort_order
	var maxSortOrder int
	err := conn.QueryRow(ctx, "SELECT COALESCE(MAX(sort_order), 0) FROM categories").Scan(&maxSortOrder)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to get max sort order: %v"}`, err),
		}, nil
	}

	// 插入新分类
	var newCategory Category
	var createdAt, updatedAt time.Time
	var icon, color sql.NullString
	var sortOrder sql.NullInt32

	query := `INSERT INTO categories (name, icon, color, sort_order)
			  VALUES ($1, $2, $3, $4)
			  RETURNING id, name, icon, color, sort_order, created_at, updated_at`

	err = conn.QueryRow(ctx, query, req.Name, req.Icon, req.Color, maxSortOrder+1).Scan(
		&newCategory.ID, &newCategory.Name, &icon, &color,
		&sortOrder, &createdAt, &updatedAt)

	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to insert category: %v"}`, err),
		}, nil
	}

	// 处理可能为 NULL 的字段
	if icon.Valid {
		newCategory.Icon = icon.String
	} else {
		newCategory.Icon = "📁"
	}

	if color.Valid {
		newCategory.Color = color.String
	} else {
		newCategory.Color = "#3B82F6"
	}

	if sortOrder.Valid {
		newCategory.SortOrder = int(sortOrder.Int32)
	} else {
		newCategory.SortOrder = 0
	}

	// 格式化时间
	newCategory.CreatedAt = createdAt.Format(time.RFC3339)
	newCategory.UpdatedAt = updatedAt.Format(time.RFC3339)

	// 构建响应
	response := APIResponse{
		Data:    &newCategory,
		Message: "Category created successfully",
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	return &events.APIGatewayProxyResponse{
		StatusCode: 201,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func handleUpdateCategory(ctx context.Context, conn *pgx.Conn, request events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
	// 获取分类 ID
	categoryIDStr := request.QueryStringParameters["id"]
	if categoryIDStr == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Category ID is required"}`,
		}, nil
	}

	categoryID, err := strconv.ParseInt(categoryIDStr, 10, 64)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Invalid category ID"}`,
		}, nil
	}

	// 解析请求体
	var req UpdateCategoryRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Invalid JSON: %v"}`, err),
		}, nil
	}

	// 构建动态更新查询
	var setParts []string
	var args []interface{}
	argIndex := 1

	if req.Name != nil && strings.TrimSpace(*req.Name) != "" {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *req.Name)
		argIndex++
	}

	if req.Icon != nil {
		setParts = append(setParts, fmt.Sprintf("icon = $%d", argIndex))
		args = append(args, *req.Icon)
		argIndex++
	}

	if req.Color != nil {
		setParts = append(setParts, fmt.Sprintf("color = $%d", argIndex))
		args = append(args, *req.Color)
		argIndex++
	}

	if req.SortOrder != nil {
		setParts = append(setParts, fmt.Sprintf("sort_order = $%d", argIndex))
		args = append(args, *req.SortOrder)
		argIndex++
	}

	if len(setParts) == 0 {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "No fields to update"}`,
		}, nil
	}

	// 添加 updated_at 字段
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// 添加 WHERE 条件的参数
	args = append(args, categoryID)

	// 构建完整的更新查询
	query := fmt.Sprintf(`UPDATE categories SET %s WHERE id = $%d 
						  RETURNING id, name, icon, color, sort_order, created_at, updated_at`,
		strings.Join(setParts, ", "), argIndex)

	// 执行更新查询
	var updatedCategory Category
	var createdAt, updatedAt time.Time
	var icon, color sql.NullString
	var sortOrder sql.NullInt32

	err = conn.QueryRow(ctx, query, args...).Scan(
		&updatedCategory.ID, &updatedCategory.Name, &icon, &color,
		&sortOrder, &createdAt, &updatedAt)

	if err != nil {
		if err.Error() == "no rows in result set" {
			return &events.APIGatewayProxyResponse{
				StatusCode: 404,
				Headers: map[string]string{
					"Content-Type":                 "application/json",
					"Access-Control-Allow-Origin":  "*",
					"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
					"Access-Control-Allow-Headers": "Content-Type, Authorization",
				},
				Body: `{"error": "Category not found"}`,
			}, nil
		}
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to update category: %v"}`, err),
		}, nil
	}

	// 处理可能为 NULL 的字段
	if icon.Valid {
		updatedCategory.Icon = icon.String
	} else {
		updatedCategory.Icon = "📁"
	}

	if color.Valid {
		updatedCategory.Color = color.String
	} else {
		updatedCategory.Color = "#3B82F6"
	}

	if sortOrder.Valid {
		updatedCategory.SortOrder = int(sortOrder.Int32)
	} else {
		updatedCategory.SortOrder = 0
	}

	// 格式化时间
	updatedCategory.CreatedAt = createdAt.Format(time.RFC3339)
	updatedCategory.UpdatedAt = updatedAt.Format(time.RFC3339)

	// 构建响应
	response := APIResponse{
		Data:    &updatedCategory,
		Message: "Category updated successfully",
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	return &events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func handleDeleteCategory(ctx context.Context, conn *pgx.Conn, request events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
	// 获取分类 ID
	categoryIDStr := request.QueryStringParameters["id"]
	if categoryIDStr == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Category ID is required"}`,
		}, nil
	}

	categoryID, err := strconv.ParseInt(categoryIDStr, 10, 64)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Invalid category ID"}`,
		}, nil
	}

	// 首先检查分类是否存在
	var exists bool
	err = conn.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM categories WHERE id = $1)", categoryID).Scan(&exists)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to check category existence: %v"}`, err),
		}, nil
	}

	if !exists {
		return &events.APIGatewayProxyResponse{
			StatusCode: 404,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Category not found"}`,
		}, nil
	}

	// 删除分类（注意：由于外键约束，相关书签的 category_id 会被设置为 NULL）
	commandTag, err := conn.Exec(ctx, "DELETE FROM categories WHERE id = $1", categoryID)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to delete category: %v"}`, err),
		}, nil
	}

	// 检查是否真的删除了记录
	if commandTag.RowsAffected() == 0 {
		return &events.APIGatewayProxyResponse{
			StatusCode: 404,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Category not found"}`,
		}, nil
	}

	// 构建响应
	response := APIResponse{
		Message: "Category deleted successfully",
		ID:      &categoryID,
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	return &events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func main() {
	lambda.Start(handler)
}
