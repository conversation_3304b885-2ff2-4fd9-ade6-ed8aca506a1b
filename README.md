# 个人导航 Chrome 插件

> 一个功能强大、界面精美的个人导航 Chrome 浏览器插件，基于 Netlify + Go 云函数 + Supabase 构建

## ✨ 功能特性

- 🔖 **智能书签管理** - 添加、编辑、删除、拖拽排序
- 📁 **分类系统** - 自定义分类图标和颜色
- 🔍 **快速搜索** - 实时搜索书签和分类
- 🎨 **精美界面** - 毛玻璃效果、流畅动画
- 🌙 **主题切换** - 支持亮色/暗色主题
- 📱 **响应式设计** - 适配各种屏幕尺寸
- ⚡ **快捷操作** - 右键菜单、快捷键支持
- 🔄 **实时同步** - 云端数据存储

## 🏗️ 技术架构

```
Chrome Extension (React + TypeScript)
           ↓
    Netlify Functions (Go)
           ↓
    Supabase (PostgreSQL)
```

## 📁 项目结构

```
navbar/
├── navbar-go/              # 后端 Go 云函数
│   ├── netlify/functions/   # API 函数
│   ├── go.mod              # Go 依赖
│   ├── netlify.toml        # 部署配置
│   └── test-api.sh         # API 测试脚本
├── navbar-extension/        # Chrome 插件前端
│   ├── src/                # 源代码
│   │   ├── components/     # React 组件
│   │   ├── services/       # API 服务
│   │   └── types/          # 类型定义
│   ├── public/             # 静态资源
│   ├── dist/               # 构建输出
│   └── package.json        # 前端依赖
└── README.md               # 项目文档
```

## 🚀 快速部署

### 前置要求

- [Node.js](https://nodejs.org/) 18+
- [Go](https://golang.org/) 1.19+
- [pnpm](https://pnpm.io/)
- [Netlify](https://netlify.com/) 账号
- [Supabase](https://supabase.com/) 账号

### 第一步：配置数据库

1. **创建 Supabase 项目**
   - 访问 [Supabase Dashboard](https://app.supabase.com/)
   - 创建新项目，选择区域 `us-east-2 (Ohio)`

2. **初始化数据库**
   
   在 SQL Editor 中执行以下脚本：

   ```sql
   -- 创建分类表
   CREATE TABLE categories (
       id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
       name TEXT NOT NULL,
       icon TEXT,
       color TEXT DEFAULT '#3B82F6',
       sort_order INTEGER DEFAULT 0,
       created_at TIMESTAMPTZ DEFAULT NOW(),
       updated_at TIMESTAMPTZ DEFAULT NOW()
   );

   -- 创建书签表
   CREATE TABLE bookmarks (
       id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
       title TEXT NOT NULL,
       url TEXT NOT NULL,
       description TEXT,
       favicon TEXT,
       category_id BIGINT REFERENCES categories(id) ON DELETE SET NULL,
       sort_order INTEGER DEFAULT 0,
       is_pinned BOOLEAN DEFAULT FALSE,
       created_at TIMESTAMPTZ DEFAULT NOW(),
       updated_at TIMESTAMPTZ DEFAULT NOW()
   );

   -- 创建用户设置表
   CREATE TABLE user_settings (
       id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
       theme TEXT DEFAULT 'light',
       layout TEXT DEFAULT 'grid',
       columns_count INTEGER DEFAULT 6,
       show_search BOOLEAN DEFAULT TRUE,
       show_categories BOOLEAN DEFAULT TRUE,
       background_image TEXT,
       created_at TIMESTAMPTZ DEFAULT NOW(),
       updated_at TIMESTAMPTZ DEFAULT NOW()
   );

   -- 禁用行级安全
   ALTER TABLE categories DISABLE ROW LEVEL SECURITY;
   ALTER TABLE bookmarks DISABLE ROW LEVEL SECURITY;
   ALTER TABLE user_settings DISABLE ROW LEVEL SECURITY;

   -- 插入默认数据
   INSERT INTO categories (name, icon, color, sort_order) VALUES
   ('常用网站', '⭐', '#F59E0B', 1),
   ('开发工具', '🛠️', '#10B981', 2),
   ('学习资源', '📚', '#3B82F6', 3),
   ('娱乐休闲', '🎮', '#8B5CF6', 4);

   INSERT INTO user_settings (theme, layout, columns_count) VALUES
   ('light', 'grid', 6);

   INSERT INTO bookmarks (title, url, description, category_id, sort_order) VALUES
   ('Google', 'https://www.google.com', '搜索引擎', 1, 1),
   ('GitHub', 'https://github.com', '代码托管平台', 2, 1),
   ('MDN', 'https://developer.mozilla.org', 'Web 开发文档', 3, 1),
   ('YouTube', 'https://www.youtube.com', '视频平台', 4, 1);
   ```

3. **获取连接字符串**
   - 进入 Settings → Database
   - 复制 Connection string
   - 格式：`postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres`

### 第二步：部署后端

1. **推送代码到 GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin https://github.com/your-username/navbar.git
   git push -u origin main
   ```

2. **部署到 Netlify**
   - 访问 [Netlify Dashboard](https://app.netlify.com/)
   - 点击 "New site from Git"
   - 选择 GitHub 仓库
   - 配置构建设置：
     - **Base directory**: `navbar-go`
     - **Build command**: `for dir in netlify/functions/*; do function_name=$(basename $dir); go build -o "netlify/functions/${function_name}/main" "./netlify/functions/${function_name}/"; done`
     - **Publish directory**: `netlify/functions`

3. **设置环境变量**
   - 在 Netlify 项目设置中添加环境变量：
   - `SUPABASE_DATABASE_URL` = 你的 Supabase 连接字符串

### 第三步：构建插件

1. **更新 API 配置**
   ```bash
   # 编辑 navbar-extension/src/services/api.ts
   # 将 API_BASE_URL 替换为你的 Netlify 函数 URL
   const API_BASE_URL = 'https://your-site-name.netlify.app/.netlify/functions';
   ```

2. **构建插件**
   ```bash
   cd navbar-extension
   pnpm install
   pnpm build
   ```

### 第四步：安装插件

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `navbar-extension/dist` 目录

🎉 **完成！** 现在你可以使用个人导航插件了。

## 🛠️ 本地开发

### 后端开发

```bash
cd navbar-go

# 创建环境变量文件
echo 'SUPABASE_DATABASE_URL="your-connection-string"' > .env

# 安装 Netlify CLI
npm install -g netlify-cli

# 本地运行
netlify dev
```

### 前端开发

```bash
cd navbar-extension

# 安装依赖
pnpm install

# 开发模式（网页版）
pnpm dev

# 构建插件
pnpm build
```

## 📚 API 文档

### 书签接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/get-bookmarks` | 获取所有书签 |
| POST | `/add-bookmark` | 添加新书签 |
| PUT | `/update-bookmark?id={id}` | 更新书签 |
| DELETE | `/delete-bookmark?id={id}` | 删除书签 |

### 分类接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/get-categories` | 获取所有分类 |
| POST | `/manage-categories` | 创建分类 |
| PUT | `/manage-categories?id={id}` | 更新分类 |
| DELETE | `/manage-categories?id={id}` | 删除分类 |

### 设置接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/get-settings` | 获取用户设置 |
| PUT | `/update-settings` | 更新用户设置 |

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `SUPABASE_DATABASE_URL` 环境变量
   - 确认 Supabase 项目状态
   - 验证连接字符串格式

2. **插件加载失败**
   - 检查 `manifest.json` 配置
   - 确认所有文件已正确构建
   - 查看 Chrome 扩展页面的错误信息

3. **API 调用失败**
   - 检查 Netlify 函数部署状态
   - 验证 API 端点 URL
   - 查看浏览器开发者工具的网络请求

### 测试 API

使用提供的测试脚本：

```bash
cd navbar-go
chmod +x test-api.sh
./test-api.sh https://your-site-name.netlify.app
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**享受你的个人导航体验！** 🚀
