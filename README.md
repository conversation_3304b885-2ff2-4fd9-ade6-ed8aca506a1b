# 个人导航 Chrome 浏览器插件

一个基于 Netlify + Go 云函数 + Supabase 的个人导航 Chrome 浏览器插件，功能类似 Infinity Pro，支持分类管理和精美的 SPA 页面展示。

## 项目结构

```
navbar/
├── navbar-go/          # 后端 Go 云函数
│   ├── netlify/
│   │   └── functions/
│   ├── go.mod
│   └── netlify.toml
├── navbar-extension/   # Chrome 浏览器插件
│   ├── src/
│   ├── public/
│   ├── manifest.json
│   └── package.json
└── README.md
```

## 功能特性

### 核心功能
- 🔖 书签管理（增删改查）
- 📁 分类管理
- 🔍 智能搜索
- 🎨 精美的 UI 设计
- 📱 响应式布局
- 🌙 主题切换
- 🔄 拖拽排序

### 技术特性
- ⚡ 基于 React 的 SPA 应用
- 🚀 Netlify 云函数后端
- 🗄️ Supabase 数据库
- 🔒 安全的 API 设计
- 📦 pnpm 包管理

## 快速开始

### 环境要求

- Node.js 16+
- Go 1.19+
- pnpm
- Netlify CLI
- Supabase 账号

### 后端部署

1. **初始化 Supabase 项目**
   ```bash
   # 在 Supabase Dashboard 中创建新项目
   # 区域选择: us-east-2 (Ohio)
   ```

2. **设置数据库**
   ```sql
   -- 在 Supabase SQL Editor 中执行
   -- 详见部署文档
   ```

3. **部署到 Netlify**
   ```bash
   cd navbar-go
   git init
   git add .
   git commit -m "Initial commit"
   git push origin main

   # 在 Netlify Dashboard 中连接 GitHub 仓库
   ```

### 插件安装

1. **构建插件**
   ```bash
   cd navbar-extension
   pnpm install
   pnpm build
   ```

2. **加载到 Chrome**
   - 打开 Chrome 扩展管理页面 `chrome://extensions/`
   - 开启开发者模式
   - 点击"加载已解压的扩展程序"
   - 选择 `navbar-extension/dist` 目录

## 详细部署指南

### 1. Supabase 数据库设置

#### 创建项目
1. 访问 [Supabase Dashboard](https://app.supabase.com/)
2. 创建新项目，选择区域 `us-east-2 (Ohio)`
3. 等待项目初始化完成

#### 数据库表结构
在 SQL Editor 中执行以下 SQL：

```sql
-- 分类表
CREATE TABLE categories (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name TEXT NOT NULL,
    icon TEXT,
    color TEXT DEFAULT '#3B82F6',
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 书签表
CREATE TABLE bookmarks (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    title TEXT NOT NULL,
    url TEXT NOT NULL,
    description TEXT,
    favicon TEXT,
    category_id BIGINT REFERENCES categories(id) ON DELETE SET NULL,
    sort_order INTEGER DEFAULT 0,
    is_pinned BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 用户设置表
CREATE TABLE user_settings (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    theme TEXT DEFAULT 'light',
    layout TEXT DEFAULT 'grid',
    columns_count INTEGER DEFAULT 6,
    show_search BOOLEAN DEFAULT TRUE,
    show_categories BOOLEAN DEFAULT TRUE,
    background_image TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 禁用 RLS（行级安全）以便云函数访问
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE bookmarks DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings DISABLE ROW LEVEL SECURITY;

-- 插入默认分类
INSERT INTO categories (name, icon, color, sort_order) VALUES
('常用网站', '⭐', '#F59E0B', 1),
('开发工具', '🛠️', '#10B981', 2),
('学习资源', '📚', '#3B82F6', 3),
('娱乐休闲', '🎮', '#8B5CF6', 4);

-- 插入默认设置
INSERT INTO user_settings (theme, layout, columns_count) VALUES
('light', 'grid', 6);

-- 插入示例书签
INSERT INTO bookmarks (title, url, description, category_id, sort_order) VALUES
('Google', 'https://www.google.com', '搜索引擎', 1, 1),
('GitHub', 'https://github.com', '代码托管平台', 2, 1),
('MDN', 'https://developer.mozilla.org', 'Web 开发文档', 3, 1),
('YouTube', 'https://www.youtube.com', '视频平台', 4, 1);
```

### 2. Netlify 环境变量配置

在 Netlify Dashboard 中设置以下环境变量：

1. 进入项目设置 → Environment variables
2. 添加变量：
   - `SUPABASE_DATABASE_URL`: Supabase 连接字符串
   - `SUPABASE_ANON_KEY`: Supabase 匿名密钥（可选）

### 3. 本地开发

#### 后端开发
```bash
cd navbar-go

# 创建 .env 文件
echo 'SUPABASE_DATABASE_URL="your-connection-string"' > .env

# 本地测试
netlify dev
```

#### 前端开发
```bash
cd navbar-extension

# 安装依赖
pnpm install

# 开发模式
pnpm dev

# 构建
pnpm build
```

## API 接口文档

### 书签管理

#### GET /get-bookmarks
获取所有书签

**响应示例：**
```json
{
  "data": [
    {
      "id": 1,
      "title": "Google",
      "url": "https://www.google.com",
      "description": "搜索引擎",
      "favicon": "https://www.google.com/favicon.ico",
      "category_id": 1,
      "sort_order": 1,
      "is_pinned": false
    }
  ]
}
```

#### POST /add-bookmark
添加新书签

**请求体：**
```json
{
  "title": "网站标题",
  "url": "https://example.com",
  "description": "网站描述",
  "category_id": 1
}
```

#### PUT /update-bookmark
更新书签

#### DELETE /delete-bookmark
删除书签

### 分类管理

#### GET /get-categories
获取所有分类

#### POST /manage-categories
管理分类（增删改）

### 用户设置

#### GET /get-settings
获取用户设置

#### PUT /update-settings
更新用户设置

## 开发指南

### 添加新功能

1. **后端 API**
   - 在 `navbar-go/netlify/functions/` 下创建新函数
   - 更新 `netlify.toml` 构建配置
   - 测试 API 接口

2. **前端功能**
   - 在 `navbar-extension/src/` 下添加组件
   - 更新路由和状态管理
   - 测试插件功能

### 代码规范

- 使用 TypeScript
- 遵循 ESLint 规则
- 编写单元测试
- 提交前运行 `pnpm lint`

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查环境变量配置
   - 确认 Supabase 项目状态
   - 验证连接字符串格式

2. **插件加载失败**
   - 检查 manifest.json 配置
   - 确认权限设置
   - 查看 Chrome 控制台错误

3. **API 调用失败**
   - 检查 CORS 配置
   - 验证 API 端点
   - 查看 Netlify 函数日志

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 许可证

MIT License

## 更新日志

### v1.0.0
- 初始版本发布
- 基础书签管理功能
- 分类管理
- Chrome 插件集成

## 完整部署步骤

### 步骤 1：准备 Supabase 数据库

1. **创建 Supabase 项目**
   - 访问 https://app.supabase.com/
   - 点击 "New project"
   - 选择组织，输入项目名称
   - 选择数据库密码
   - 选择区域（推荐 us-east-2）
   - 等待项目创建完成

2. **执行数据库初始化脚本**
   - 进入项目 Dashboard
   - 点击左侧 "SQL Editor"
   - 复制并执行上面提供的完整 SQL 脚本

3. **获取连接字符串**
   - 进入 Settings → Database
   - 复制 "Connection string"
   - 格式：`postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres`

### 步骤 2：部署后端到 Netlify

1. **准备代码仓库**
   ```bash
   # 克隆或下载项目代码
   git clone <your-repo-url>
   cd navbar

   # 如果是新项目，初始化 Git
   git init
   git add .
   git commit -m "Initial commit"
   ```

2. **推送到 GitHub**
   ```bash
   # 创建 GitHub 仓库后
   git remote add origin https://github.com/your-username/navbar.git
   git branch -M main
   git push -u origin main
   ```

3. **在 Netlify 部署**
   - 访问 https://app.netlify.com/
   - 点击 "New site from Git"
   - 选择 GitHub，授权并选择仓库
   - 配置构建设置：
     - Base directory: `navbar-go`
     - Build command: `for dir in netlify/functions/*; do function_name=$(basename $dir); go build -o "netlify/functions/${function_name}/main" "./netlify/functions/${function_name}/"; done`
     - Publish directory: `netlify/functions`
   - 点击 "Deploy site"

4. **设置环境变量**
   - 在 Netlify 项目中，进入 Site settings → Environment variables
   - 点击 "Add variable"
   - 添加：
     - Key: `SUPABASE_DATABASE_URL`
     - Value: 你的 Supabase 连接字符串
   - 保存后重新部署

### 步骤 3：配置和构建 Chrome 插件

1. **更新 API 配置**
   ```bash
   cd navbar-extension

   # 编辑 src/services/api.ts
   # 将 API_BASE_URL 替换为你的 Netlify 函数 URL
   # 例如：https://amazing-site-123456.netlify.app/.netlify/functions
   ```

2. **安装依赖并构建**
   ```bash
   # 安装依赖
   pnpm install

   # 构建插件
   pnpm build

   # 检查构建结果
   ls dist/
   ```

### 步骤 4：安装 Chrome 插件

1. **加载插件到 Chrome**
   - 打开 Chrome 浏览器
   - 访问 `chrome://extensions/`
   - 开启右上角的 "开发者模式"
   - 点击 "加载已解压的扩展程序"
   - 选择 `navbar-extension/dist` 文件夹
   - 插件安装成功

2. **测试插件功能**
   - 打开新标签页，应该显示个人导航页面
   - 点击浏览器工具栏中的插件图标
   - 尝试添加当前页面为书签
   - 测试右键菜单功能

### 步骤 5：验证部署

1. **测试 API 接口**
   ```bash
   # 测试获取书签
   curl https://your-site.netlify.app/.netlify/functions/get-bookmarks

   # 测试获取分类
   curl https://your-site.netlify.app/.netlify/functions/get-categories
   ```

2. **检查插件功能**
   - 新标签页是否正常显示
   - 能否添加、编辑、删除书签
   - 分类功能是否正常
   - 搜索功能是否工作
   - 主题切换是否生效

### 故障排除

**常见问题及解决方案：**

1. **Netlify 构建失败**
   - 检查 Go 版本是否兼容
   - 确认 netlify.toml 配置正确
   - 查看构建日志中的错误信息

2. **数据库连接失败**
   - 验证 SUPABASE_DATABASE_URL 格式
   - 确认密码中的特殊字符已正确编码
   - 检查 Supabase 项目是否正常运行

3. **插件无法加载**
   - 确认 manifest.json 格式正确
   - 检查所有必需的文件是否存在
   - 查看 Chrome 扩展页面的错误信息

4. **API 调用失败**
   - 检查 CORS 设置
   - 确认 API 端点 URL 正确
   - 查看浏览器开发者工具的网络请求

### 生产环境优化

1. **性能优化**
   - 启用 Netlify 的 CDN 和缓存
   - 优化图片和静态资源
   - 使用 Gzip 压缩

2. **安全设置**
   - 配置适当的 CORS 策略
   - 使用 HTTPS
   - 定期更新依赖包

3. **监控和日志**
   - 设置 Netlify 函数监控
   - 配置错误报告
   - 定期检查性能指标

恭喜！你的个人导航 Chrome 插件现在已经成功部署并可以使用了。