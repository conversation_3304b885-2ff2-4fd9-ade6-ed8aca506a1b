# NULL 字段扫描错误修复

## 问题描述

在测试 API 函数时，`get-bookmarks` 和 `get-settings` 函数返回以下错误：

```json
{
  "error": "Failed to scan row: can't scan into dest[4] (col: favicon): cannot scan NULL into *string"
}
```

## 问题原因

Go 语言中，当数据库字段可能为 NULL 时，不能直接扫描到 `string` 类型变量中。需要使用 `database/sql` 包中的 `sql.NullString` 类型来处理 NULL 值。

## 修复的文件

### 1. get-bookmarks/main.go
- **问题字段**: `description`, `favicon` (可能为 NULL)
- **修复方法**: 使用 `sql.NullString` 扫描，然后检查 `Valid` 字段

### 2. get-settings/main.go
- **问题字段**: `background_image` (可能为 NULL)
- **修复方法**: 使用 `sql.NullString` 扫描，然后检查 `Valid` 字段
- **修复函数**: `handler()` 和 `createDefaultSettings()`

### 3. add-bookmark/main.go
- **问题字段**: `description`, `favicon` (可能为 NULL)
- **修复方法**: 使用 `sql.NullString` 扫描，然后检查 `Valid` 字段

### 4. update-bookmark/main.go
- **问题字段**: `description`, `favicon` (可能为 NULL)
- **修复方法**: 使用 `sql.NullString` 扫描，然后检查 `Valid` 字段

### 5. update-settings/main.go
- **问题字段**: `background_image` (可能为 NULL)
- **修复方法**: 使用 `sql.NullString` 扫描，然后检查 `Valid` 字段
- **修复函数**: 主更新函数和 `createAndUpdateSettings()`

## 修复模式

### 修复前
```go
var bookmark Bookmark
err := rows.Scan(&bookmark.ID, &bookmark.Title, &bookmark.URL, 
    &bookmark.Description, &bookmark.Favicon, ...)
```

### 修复后
```go
var bookmark Bookmark
var description, favicon sql.NullString

err := rows.Scan(&bookmark.ID, &bookmark.Title, &bookmark.URL, 
    &description, &favicon, ...)

// 处理可能为 NULL 的字段
if description.Valid {
    bookmark.Description = description.String
} else {
    bookmark.Description = ""
}

if favicon.Valid {
    bookmark.Favicon = favicon.String
} else {
    bookmark.Favicon = ""
}
```

## 数据库字段分析

### bookmarks 表
- `description` - TEXT (可为 NULL)
- `favicon` - TEXT (可为 NULL)

### user_settings 表
- `background_image` - TEXT (可为 NULL)

### categories 表
- 所有字段都有 NOT NULL 约束或默认值，无需修复

## 测试验证

修复后，以下 API 应该正常工作：
- ✅ `GET /get-categories` (已正常)
- ✅ `GET /get-bookmarks` (已修复)
- ✅ `GET /get-settings` (已修复)
- ✅ `POST /add-bookmark` (已修复)
- ✅ `PUT /update-bookmark` (已修复)
- ✅ `PUT /update-settings` (已修复)

## 部署说明

修复完成后，需要重新部署 Netlify 函数：

1. 提交代码更改
2. 推送到 GitHub
3. Netlify 会自动重新构建和部署
4. 使用 `test-api.sh` 脚本验证修复效果

```bash
cd navbar-go
./test-api.sh https://your-site-name.netlify.app
```

所有 API 接口现在应该能正常处理 NULL 字段了。
