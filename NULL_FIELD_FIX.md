# NULL 字段扫描错误修复

## 问题描述

在测试 API 函数时，多个函数返回以下错误：

```json
{
  "error": "Failed to scan row: can't scan into dest[X]: cannot scan NULL into *string"
}
```

## 问题原因

Go 语言中，当数据库字段可能为 NULL 时，不能直接扫描到 `string`、`int`、`bool` 等基本类型变量中。需要使用 `database/sql` 包中的 `sql.NullString`、`sql.NullInt32`、`sql.NullBool` 等类型来处理 NULL 值。

**重要**: 在 PostgreSQL 中，即使字段有 DEFAULT 值，也不意味着它是 NOT NULL。所有没有明确声明 NOT NULL 的字段都可能为 NULL。

## 数据库字段分析

### categories 表
- `name` - TEXT NOT NULL ✅ (可直接使用 string)
- `icon` - TEXT ❌ (可为 NULL，需要 sql.NullString)
- `color` - TEXT DEFAULT '#3B82F6' ❌ (可为 NULL，需要 sql.NullString)
- `sort_order` - INTEGER DEFAULT 0 ❌ (可为 NULL，需要 sql.NullInt32)

### bookmarks 表
- `title` - TEXT NOT NULL ✅ (可直接使用 string)
- `url` - TEXT NOT NULL ✅ (可直接使用 string)
- `description` - TEXT ❌ (可为 NULL，需要 sql.NullString)
- `favicon` - TEXT ❌ (可为 NULL，需要 sql.NullString)
- `category_id` - BIGINT REFERENCES ❌ (可为 NULL，需要 sql.NullInt64)
- `sort_order` - INTEGER DEFAULT 0 ❌ (可为 NULL，需要 sql.NullInt32)
- `is_pinned` - BOOLEAN DEFAULT FALSE ❌ (可为 NULL，需要 sql.NullBool)

### user_settings 表
- `theme` - TEXT DEFAULT 'light' ❌ (可为 NULL，需要 sql.NullString)
- `layout` - TEXT DEFAULT 'grid' ❌ (可为 NULL，需要 sql.NullString)
- `columns_count` - INTEGER DEFAULT 6 ❌ (可为 NULL，需要 sql.NullInt32)
- `show_search` - BOOLEAN DEFAULT TRUE ❌ (可为 NULL，需要 sql.NullBool)
- `show_categories` - BOOLEAN DEFAULT TRUE ❌ (可为 NULL，需要 sql.NullBool)
- `background_image` - TEXT ❌ (可为 NULL，需要 sql.NullString)

## 修复的文件

### 1. get-categories/main.go
- **问题字段**: `icon`, `color`, `sort_order`
- **修复方法**: 使用相应的 Null 类型扫描

### 2. get-bookmarks/main.go
- **问题字段**: `description`, `favicon`, `category_id`, `sort_order`, `is_pinned`
- **修复方法**: 使用相应的 Null 类型扫描

### 3. get-settings/main.go
- **问题字段**: `theme`, `layout`, `columns_count`, `show_search`, `show_categories`, `background_image`
- **修复方法**: 使用相应的 Null 类型扫描
- **修复函数**: `handler()` 和 `createDefaultSettings()`

### 4. add-bookmark/main.go
- **问题字段**: `description`, `favicon`, `category_id`, `sort_order`, `is_pinned`
- **修复方法**: 使用相应的 Null 类型扫描

### 5. update-bookmark/main.go
- **问题字段**: `description`, `favicon`, `category_id`, `sort_order`, `is_pinned`
- **修复方法**: 使用相应的 Null 类型扫描

### 6. update-settings/main.go
- **问题字段**: `theme`, `layout`, `columns_count`, `show_search`, `show_categories`, `background_image`
- **修复方法**: 使用相应的 Null 类型扫描
- **修复函数**: 主更新函数和 `createAndUpdateSettings()`

### 7. manage-categories/main.go
- **问题字段**: `icon`, `color`, `sort_order`
- **修复方法**: 使用相应的 Null 类型扫描
- **修复函数**: `handleCreateCategory()` 和 `handleUpdateCategory()`

## 修复模式

### 修复前（会出错）
```go
var bookmark Bookmark
err := rows.Scan(&bookmark.ID, &bookmark.Title, &bookmark.URL,
    &bookmark.Description, &bookmark.Favicon, &bookmark.CategoryID,
    &bookmark.SortOrder, &bookmark.IsPinned, ...)
```

### 修复后（正确处理 NULL）
```go
var bookmark Bookmark
var description, favicon sql.NullString
var categoryID sql.NullInt64
var sortOrder sql.NullInt32
var isPinned sql.NullBool

err := rows.Scan(&bookmark.ID, &bookmark.Title, &bookmark.URL,
    &description, &favicon, &categoryID, &sortOrder, &isPinned, ...)

// 处理可能为 NULL 的字段
if description.Valid {
    bookmark.Description = description.String
} else {
    bookmark.Description = ""
}

if favicon.Valid {
    bookmark.Favicon = favicon.String
} else {
    bookmark.Favicon = ""
}

if categoryID.Valid {
    bookmark.CategoryID = &categoryID.Int64
} else {
    bookmark.CategoryID = nil
}

if sortOrder.Valid {
    bookmark.SortOrder = int(sortOrder.Int32)
} else {
    bookmark.SortOrder = 0
}

if isPinned.Valid {
    bookmark.IsPinned = isPinned.Bool
} else {
    bookmark.IsPinned = false
}
```

### 支持的 NULL 类型
- `sql.NullString` - 用于 TEXT 字段
- `sql.NullInt32` - 用于 INTEGER 字段
- `sql.NullInt64` - 用于 BIGINT 字段
- `sql.NullBool` - 用于 BOOLEAN 字段
- `sql.NullFloat64` - 用于 FLOAT 字段
- `sql.NullTime` - 用于 TIMESTAMP 字段

## 测试验证

修复后，以下 API 应该正常工作：
- ✅ `GET /get-categories` (已修复所有 NULL 字段)
- ✅ `GET /get-bookmarks` (已修复所有 NULL 字段)
- ✅ `GET /get-settings` (已修复所有 NULL 字段)
- ✅ `POST /add-bookmark` (已修复所有 NULL 字段)
- ✅ `PUT /update-bookmark` (已修复所有 NULL 字段)
- ✅ `PUT /update-settings` (已修复所有 NULL 字段)
- ✅ `POST /manage-categories` (已修复所有 NULL 字段)
- ✅ `PUT /manage-categories` (已修复所有 NULL 字段)
- ✅ `DELETE /manage-categories` (无需修复，不涉及扫描)
- ✅ `DELETE /delete-bookmark` (无需修复，不涉及扫描)

## 部署说明

修复完成后，需要重新部署 Netlify 函数：

1. 提交代码更改
2. 推送到 GitHub
3. Netlify 会自动重新构建和部署
4. 使用 `test-api.sh` 脚本验证修复效果

```bash
cd navbar-go
./test-api.sh https://your-site-name.netlify.app
```

所有 API 接口现在应该能正常处理 NULL 字段了。
