// content.ts - Chrome 扩展内容脚本

// 监听来自背景脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Content script received message:', message);
  
  switch (message.action) {
    case 'bookmarkAdded':
      showNotification('书签添加成功！', 'success');
      break;
      
    case 'bookmarkDeleted':
      showNotification('书签删除成功！', 'info');
      break;
      
    case 'bookmarkError':
      showNotification('操作失败：' + message.error, 'error');
      break;
      
    case 'getPageInfo':
      // 返回当前页面信息
      sendResponse({
        title: document.title,
        url: window.location.href,
        description: getPageDescription(),
        favicon: getFaviconUrl(),
      });
      break;
      
    default:
      sendResponse({ received: true });
  }
});

// 获取页面描述
function getPageDescription(): string {
  // 尝试从 meta 标签获取描述
  const metaDescription = document.querySelector('meta[name="description"]') as HTMLMetaElement;
  if (metaDescription && metaDescription.content) {
    return metaDescription.content.trim();
  }
  
  // 尝试从 Open Graph 标签获取描述
  const ogDescription = document.querySelector('meta[property="og:description"]') as HTMLMetaElement;
  if (ogDescription && ogDescription.content) {
    return ogDescription.content.trim();
  }
  
  // 尝试从页面第一段文字获取描述
  const firstParagraph = document.querySelector('p');
  if (firstParagraph && firstParagraph.textContent) {
    const text = firstParagraph.textContent.trim();
    return text.length > 200 ? text.substring(0, 200) + '...' : text;
  }
  
  return '';
}

// 获取页面 favicon URL
function getFaviconUrl(): string {
  // 尝试从 link 标签获取 favicon
  const faviconLink = document.querySelector('link[rel*="icon"]') as HTMLLinkElement;
  if (faviconLink && faviconLink.href) {
    return faviconLink.href;
  }
  
  // 使用默认的 favicon 路径
  const defaultFavicon = `${window.location.protocol}//${window.location.hostname}/favicon.ico`;
  return defaultFavicon;
}

// 显示通知
function showNotification(message: string, type: 'success' | 'error' | 'info' = 'info') {
  // 检查是否已存在通知容器
  let container = document.getElementById('navbar-extension-notifications');
  
  if (!container) {
    container = document.createElement('div');
    container.id = 'navbar-extension-notifications';
    container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      pointer-events: none;
    `;
    document.body.appendChild(container);
  }
  
  // 创建通知元素
  const notification = document.createElement('div');
  notification.style.cssText = `
    margin-bottom: 10px;
    padding: 12px 16px;
    border-radius: 8px;
    color: white;
    font-family: system-ui, -apple-system, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.3s ease;
    transform: translateX(100%);
    opacity: 0;
    max-width: 300px;
    word-wrap: break-word;
    background: ${getNotificationColor(type)};
  `;
  
  notification.textContent = message;
  container.appendChild(notification);
  
  // 动画显示
  requestAnimationFrame(() => {
    notification.style.transform = 'translateX(0)';
    notification.style.opacity = '1';
  });
  
  // 点击关闭
  notification.addEventListener('click', () => {
    removeNotification(notification);
  });
  
  // 自动关闭
  setTimeout(() => {
    removeNotification(notification);
  }, 5000);
}

// 获取通知颜色
function getNotificationColor(type: string): string {
  switch (type) {
    case 'success':
      return 'linear-gradient(135deg, #10b981, #059669)';
    case 'error':
      return 'linear-gradient(135deg, #ef4444, #dc2626)';
    case 'info':
    default:
      return 'linear-gradient(135deg, #3b82f6, #2563eb)';
  }
}

// 移除通知
function removeNotification(notification: HTMLElement) {
  notification.style.transform = 'translateX(100%)';
  notification.style.opacity = '0';
  
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 300);
}

// 添加快捷键支持
document.addEventListener('keydown', (event) => {
  // Ctrl+Shift+B 或 Cmd+Shift+B 快速添加当前页面
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'B') {
    event.preventDefault();
    
    chrome.runtime.sendMessage({
      action: 'addBookmark',
      data: {
        title: document.title,
        url: window.location.href,
        description: getPageDescription(),
        favicon: getFaviconUrl(),
      },
    });
  }
  
  // Ctrl+Shift+N 或 Cmd+Shift+N 打开导航页面
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'N') {
    event.preventDefault();
    
    chrome.runtime.sendMessage({
      action: 'openNavigation',
    });
  }
});

// 监听页面加载完成
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  initializeContentScript();
}

function initializeContentScript() {
  console.log('Navbar extension content script initialized');
  
  // 可以在这里添加页面加载完成后的初始化逻辑
  // 例如：检查页面是否已经被收藏，显示相应的提示等
}

// 导出空对象以使其成为模块
export {};
