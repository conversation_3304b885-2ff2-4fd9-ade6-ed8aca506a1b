// netlify/functions/update-bookmark/main.go
package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/jackc/pgx/v5"
)

// UpdateBookmarkRequest 更新书签请求结构体
type UpdateBookmarkRequest struct {
	Title       *string `json:"title"`
	URL         *string `json:"url"`
	Description *string `json:"description"`
	CategoryID  *int64  `json:"category_id"`
	Favicon     *string `json:"favicon"`
	SortOrder   *int    `json:"sort_order"`
	IsPinned    *bool   `json:"is_pinned"`
}

// Bookmark 书签结构体
type Bookmark struct {
	ID          int64  `json:"id"`
	Title       string `json:"title"`
	URL         string `json:"url"`
	Description string `json:"description"`
	Favicon     string `json:"favicon"`
	CategoryID  *int64 `json:"category_id"`
	SortOrder   int    `json:"sort_order"`
	IsPinned    bool   `json:"is_pinned"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// APIResponse API 响应结构体
type APIResponse struct {
	Data    *Bookmark `json:"data"`
	Message string    `json:"message"`
}

func handler(request events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
	ctx := context.Background()

	// 处理 CORS 预检请求
	if request.HTTPMethod == "OPTIONS" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 200,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: "",
		}, nil
	}

	// 只允许 PUT 请求
	if request.HTTPMethod != "PUT" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 405,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Method not allowed"}`,
		}, nil
	}

	// 获取书签 ID
	bookmarkIDStr := request.QueryStringParameters["id"]
	if bookmarkIDStr == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Bookmark ID is required"}`,
		}, nil
	}

	bookmarkID, err := strconv.ParseInt(bookmarkIDStr, 10, 64)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Invalid bookmark ID"}`,
		}, nil
	}

	// 从环境变量中读取数据库连接字符串
	connString := os.Getenv("SUPABASE_DATABASE_URL")
	if connString == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "SUPABASE_DATABASE_URL not set"}`,
		}, nil
	}

	// 解析请求体
	var req UpdateBookmarkRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Invalid JSON: %v"}`, err),
		}, nil
	}

	// 连接数据库
	conn, err := pgx.Connect(ctx, connString)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Unable to connect to database: %v"}`, err),
		}, nil
	}
	defer conn.Close(ctx)

	// 构建动态更新查询
	var setParts []string
	var args []interface{}
	argIndex := 1

	if req.Title != nil && strings.TrimSpace(*req.Title) != "" {
		setParts = append(setParts, fmt.Sprintf("title = $%d", argIndex))
		args = append(args, *req.Title)
		argIndex++
	}

	if req.URL != nil && strings.TrimSpace(*req.URL) != "" {
		setParts = append(setParts, fmt.Sprintf("url = $%d", argIndex))
		args = append(args, *req.URL)
		argIndex++
	}

	if req.Description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argIndex))
		args = append(args, *req.Description)
		argIndex++
	}

	if req.CategoryID != nil {
		setParts = append(setParts, fmt.Sprintf("category_id = $%d", argIndex))
		args = append(args, *req.CategoryID)
		argIndex++
	}

	if req.Favicon != nil {
		setParts = append(setParts, fmt.Sprintf("favicon = $%d", argIndex))
		args = append(args, *req.Favicon)
		argIndex++
	}

	if req.SortOrder != nil {
		setParts = append(setParts, fmt.Sprintf("sort_order = $%d", argIndex))
		args = append(args, *req.SortOrder)
		argIndex++
	}

	if req.IsPinned != nil {
		setParts = append(setParts, fmt.Sprintf("is_pinned = $%d", argIndex))
		args = append(args, *req.IsPinned)
		argIndex++
	}

	if len(setParts) == 0 {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "No fields to update"}`,
		}, nil
	}

	// 添加 updated_at 字段
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// 添加 WHERE 条件的参数
	args = append(args, bookmarkID)

	// 构建完整的更新查询
	query := fmt.Sprintf(`UPDATE bookmarks SET %s WHERE id = $%d 
						  RETURNING id, title, url, description, favicon, category_id, sort_order, is_pinned, created_at, updated_at`,
		strings.Join(setParts, ", "), argIndex)

	// 执行更新查询
	var updatedBookmark Bookmark
	var createdAt, updatedAt time.Time
	var description, favicon sql.NullString

	err = conn.QueryRow(ctx, query, args...).Scan(
		&updatedBookmark.ID, &updatedBookmark.Title, &updatedBookmark.URL, &description,
		&favicon, &updatedBookmark.CategoryID, &updatedBookmark.SortOrder,
		&updatedBookmark.IsPinned, &createdAt, &updatedAt)

	if err != nil {
		if err.Error() == "no rows in result set" {
			return &events.APIGatewayProxyResponse{
				StatusCode: 404,
				Headers: map[string]string{
					"Content-Type":                 "application/json",
					"Access-Control-Allow-Origin":  "*",
					"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
					"Access-Control-Allow-Headers": "Content-Type, Authorization",
				},
				Body: `{"error": "Bookmark not found"}`,
			}, nil
		}
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to update bookmark: %v"}`, err),
		}, nil
	}

	// 处理可能为 NULL 的字段
	if description.Valid {
		updatedBookmark.Description = description.String
	} else {
		updatedBookmark.Description = ""
	}

	if favicon.Valid {
		updatedBookmark.Favicon = favicon.String
	} else {
		updatedBookmark.Favicon = ""
	}

	// 格式化时间
	updatedBookmark.CreatedAt = createdAt.Format(time.RFC3339)
	updatedBookmark.UpdatedAt = updatedAt.Format(time.RFC3339)

	// 构建响应
	response := APIResponse{
		Data:    &updatedBookmark,
		Message: "Bookmark updated successfully",
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	return &events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func main() {
	lambda.Start(handler)
}
