<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽排序测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .sortable-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .sortable-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            font-weight: bold;
            cursor: move;
            transition: all 0.3s ease;
            user-select: none;
            position: relative;
        }
        
        .sortable-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .sortable-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }
        
        .sort-order {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>拖拽排序功能测试</h1>
    
    <div class="test-section">
        <h2>模拟书签列表</h2>
        <p>拖拽下面的卡片来改变排序，系统会自动保存新的排序到服务器。</p>
        
        <div class="controls">
            <button class="btn" onclick="resetOrder()">重置排序</button>
            <button class="btn" onclick="randomizeOrder()">随机排序</button>
            <button class="btn" onclick="testBatchUpdate()">测试批量更新</button>
            <button class="btn" onclick="clearLog()">清除日志</button>
        </div>
        
        <div id="status" class="status info">
            准备就绪 - 拖拽卡片来测试排序功能
        </div>
        
        <div id="sortable-list" class="sortable-list">
            <!-- 卡片将通过 JavaScript 生成 -->
        </div>
    </div>

    <div class="test-section">
        <h2>操作日志</h2>
        <div id="log-area" class="log-area"></div>
    </div>

    <div class="test-section">
        <h2>API 测试</h2>
        <div class="controls">
            <button class="btn" onclick="testAPI()">测试批量更新API</button>
        </div>
        <div id="api-log" class="log-area"></div>
    </div>

    <script>
        let items = [
            { id: 1, title: 'Google', sort_order: 0 },
            { id: 2, title: 'GitHub', sort_order: 1 },
            { id: 3, title: 'MDN', sort_order: 2 },
            { id: 4, title: 'YouTube', sort_order: 3 },
            { id: 5, title: 'Stack Overflow', sort_order: 4 },
            { id: 6, title: 'Reddit', sort_order: 5 },
            { id: 7, title: 'Twitter', sort_order: 6 },
            { id: 8, title: 'LinkedIn', sort_order: 7 }
        ];

        let draggedElement = null;
        let draggedIndex = -1;

        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            
            // 更新状态
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function apiLog(message) {
            const apiLogArea = document.getElementById('api-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            apiLogArea.textContent += logEntry;
            apiLogArea.scrollTop = apiLogArea.scrollHeight;
        }

        function renderItems() {
            const container = document.getElementById('sortable-list');
            container.innerHTML = '';
            
            items.forEach((item, index) => {
                const div = document.createElement('div');
                div.className = 'sortable-item';
                div.draggable = true;
                div.dataset.id = item.id;
                div.dataset.index = index;
                div.innerHTML = `
                    <div class="sort-order">${item.sort_order}</div>
                    <div>${item.title}</div>
                    <small>ID: ${item.id}</small>
                `;
                
                // 拖拽事件
                div.addEventListener('dragstart', handleDragStart);
                div.addEventListener('dragover', handleDragOver);
                div.addEventListener('drop', handleDrop);
                div.addEventListener('dragend', handleDragEnd);
                
                container.appendChild(div);
            });
        }

        function handleDragStart(e) {
            draggedElement = e.target;
            draggedIndex = parseInt(e.target.dataset.index);
            e.target.classList.add('dragging');
            log(`开始拖拽: ${items[draggedIndex].title}`);
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDrop(e) {
            e.preventDefault();
            
            if (!draggedElement) return;
            
            const dropIndex = parseInt(e.target.closest('.sortable-item').dataset.index);
            
            if (draggedIndex === dropIndex) return;
            
            log(`拖拽 ${items[draggedIndex].title} 到位置 ${dropIndex}`);
            
            // 重新排列数组
            const draggedItem = items[draggedIndex];
            items.splice(draggedIndex, 1);
            items.splice(dropIndex, 0, draggedItem);
            
            // 更新 sort_order
            items.forEach((item, index) => {
                item.sort_order = index;
            });
            
            // 重新渲染
            renderItems();
            
            // 模拟批量更新
            simulateBatchUpdate();
        }

        function handleDragEnd(e) {
            e.target.classList.remove('dragging');
            draggedElement = null;
            draggedIndex = -1;
        }

        function simulateBatchUpdate() {
            const updates = items.map(item => ({
                id: item.id,
                sort_order: item.sort_order
            }));
            
            log(`模拟批量更新: ${updates.length} 个项目`, 'info');
            console.log('Batch update data:', { updates });
            
            // 模拟API调用延迟
            setTimeout(() => {
                log('排序更新成功！', 'success');
            }, 500);
        }

        function resetOrder() {
            items.forEach((item, index) => {
                item.sort_order = index;
            });
            items.sort((a, b) => a.id - b.id);
            items.forEach((item, index) => {
                item.sort_order = index;
            });
            renderItems();
            log('排序已重置', 'info');
        }

        function randomizeOrder() {
            // Fisher-Yates 洗牌算法
            for (let i = items.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [items[i], items[j]] = [items[j], items[i]];
            }
            
            // 更新 sort_order
            items.forEach((item, index) => {
                item.sort_order = index;
            });
            
            renderItems();
            log('排序已随机化', 'info');
        }

        function testBatchUpdate() {
            const updates = items.map(item => ({
                id: item.id,
                sort_order: item.sort_order
            }));
            
            log('测试批量更新数据结构:', 'info');
            log(JSON.stringify({ updates }, null, 2), 'info');
        }

        function clearLog() {
            document.getElementById('log-area').textContent = '';
            document.getElementById('api-log').textContent = '';
            log('日志已清除', 'info');
        }

        async function testAPI() {
            apiLog('开始测试批量更新API...');
            
            const updates = items.map(item => ({
                id: item.id,
                sort_order: item.sort_order
            }));
            
            const requestData = { updates };
            
            try {
                apiLog('发送请求: ' + JSON.stringify(requestData, null, 2));
                
                const response = await fetch('https://navbar-ventix.netlify.app/.netlify/functions/batch-update-sort-order', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                apiLog(`响应状态: ${response.status} ${response.statusText}`);
                
                const result = await response.json();
                apiLog('响应数据: ' + JSON.stringify(result, null, 2));
                
                if (result.success) {
                    apiLog('✅ API测试成功！');
                    log('API测试成功', 'success');
                } else {
                    apiLog('❌ API测试失败: ' + result.message);
                    log('API测试失败', 'error');
                }
                
            } catch (error) {
                apiLog('❌ API请求异常: ' + error.message);
                log('API请求异常', 'error');
            }
        }

        // 初始化
        renderItems();
        log('拖拽排序测试页面已加载', 'success');
    </script>
</body>
</html>
