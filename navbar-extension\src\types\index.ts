// 书签类型
export interface Bookmark {
  id: number;
  title: string;
  url: string;
  description?: string;
  favicon?: string;
  category_id?: number;
  sort_order: number;
  is_pinned: boolean;
  created_at: string;
  updated_at: string;
}

// 分类类型
export interface Category {
  id: number;
  name: string;
  icon: string;
  color: string;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 用户设置类型
export interface UserSettings {
  id: number;
  theme: 'light' | 'dark';
  layout: 'grid' | 'list';
  columns_count: number;
  show_search: boolean;
  show_categories: boolean;
  background_image?: string;
  created_at: string;
  updated_at: string;
}

// API 响应类型
export interface APIResponse<T> {
  data: T;
  message?: string;
  connection_time_ms?: string;
  query_time_ms?: string;
  total?: number;
}

// 书签请求类型
export interface AddBookmarkRequest {
  title: string;
  url: string;
  description?: string;
  category_id?: number;
  favicon?: string;
}

export interface UpdateBookmarkRequest {
  title?: string;
  url?: string;
  description?: string;
  category_id?: number;
  favicon?: string;
  sort_order?: number;
  is_pinned?: boolean;
}

// 分类请求类型
export interface CreateCategoryRequest {
  name: string;
  icon: string;
  color: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  icon?: string;
  color?: string;
  sort_order?: number;
}

// 设置请求类型
export interface UpdateSettingsRequest {
  theme?: 'light' | 'dark';
  layout?: 'grid' | 'list';
  columns_count?: number;
  show_search?: boolean;
  show_categories?: boolean;
  background_image?: string;
}

// 应用状态类型
export interface AppState {
  bookmarks: Bookmark[];
  categories: Category[];
  settings: UserSettings;
  loading: boolean;
  error: string | null;
  searchQuery: string;
  selectedCategory: number | null;
}

// 拖拽类型
export interface DragItem {
  id: number;
  type: 'bookmark' | 'category';
  index: number;
}
