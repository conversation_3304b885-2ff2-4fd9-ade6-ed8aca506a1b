import { Bookmark } from '../types';

class ChromeService {
  // 检查是否在 Chrome 扩展环境中
  private isExtensionContext(): boolean {
    return typeof chrome !== 'undefined' && chrome.bookmarks;
  }

  // 获取 Chrome 书签
  async getChromeBookmarks(): Promise<chrome.bookmarks.BookmarkTreeNode[]> {
    if (!this.isExtensionContext()) {
      return [];
    }

    return new Promise((resolve) => {
      chrome.bookmarks.getTree((bookmarkTree) => {
        resolve(bookmarkTree);
      });
    });
  }

  // 将 Chrome 书签转换为我们的书签格式
  convertChromeBookmarks(chromeBookmarks: chrome.bookmarks.BookmarkTreeNode[]): Bookmark[] {
    const bookmarks: Bookmark[] = [];
    
    const traverse = (nodes: chrome.bookmarks.BookmarkTreeNode[]) => {
      nodes.forEach((node) => {
        if (node.url) {
          // 这是一个书签
          bookmarks.push({
            id: parseInt(node.id),
            title: node.title,
            url: node.url,
            description: '',
            favicon: this.getFaviconFromUrl(node.url),
            category_id: undefined,
            sort_order: bookmarks.length,
            is_pinned: false,
            created_at: new Date(node.dateAdded || Date.now()).toISOString(),
            updated_at: new Date(node.dateGroupModified || node.dateAdded || Date.now()).toISOString(),
          });
        } else if (node.children) {
          // 这是一个文件夹，递归处理
          traverse(node.children);
        }
      });
    };

    traverse(chromeBookmarks);
    return bookmarks;
  }

  // 从 URL 获取 favicon
  private getFaviconFromUrl(url: string): string {
    try {
      const domain = new URL(url).hostname;
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
    } catch {
      return '/icons/default-favicon.png';
    }
  }

  // 添加书签到 Chrome
  async addChromeBookmark(title: string, url: string): Promise<chrome.bookmarks.BookmarkTreeNode | null> {
    if (!this.isExtensionContext()) {
      return null;
    }

    return new Promise((resolve) => {
      chrome.bookmarks.create({
        title,
        url,
      }, (bookmark) => {
        resolve(bookmark || null);
      });
    });
  }

  // 从 Chrome 删除书签
  async removeChromeBookmark(id: string): Promise<boolean> {
    if (!this.isExtensionContext()) {
      return false;
    }

    return new Promise((resolve) => {
      chrome.bookmarks.remove(id, () => {
        resolve(!chrome.runtime.lastError);
      });
    });
  }

  // 获取当前标签页信息
  async getCurrentTab(): Promise<chrome.tabs.Tab | null> {
    if (!this.isExtensionContext()) {
      return null;
    }

    return new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        resolve(tabs[0] || null);
      });
    });
  }

  // 打开新标签页
  async openTab(url: string): Promise<chrome.tabs.Tab | null> {
    if (!this.isExtensionContext()) {
      // 在非扩展环境中，使用 window.open
      window.open(url, '_blank');
      return null;
    }

    return new Promise((resolve) => {
      chrome.tabs.create({ url }, (tab) => {
        resolve(tab || null);
      });
    });
  }

  // 存储数据到 Chrome 存储
  async setStorage(key: string, value: any): Promise<void> {
    if (!this.isExtensionContext()) {
      // 在非扩展环境中，使用 localStorage
      localStorage.setItem(key, JSON.stringify(value));
      return;
    }

    return new Promise((resolve) => {
      chrome.storage.local.set({ [key]: value }, () => {
        resolve();
      });
    });
  }

  // 从 Chrome 存储获取数据
  async getStorage(key: string): Promise<any> {
    if (!this.isExtensionContext()) {
      // 在非扩展环境中，使用 localStorage
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    }

    return new Promise((resolve) => {
      chrome.storage.local.get([key], (result) => {
        resolve(result[key] || null);
      });
    });
  }

  // 监听存储变化
  onStorageChanged(callback: (changes: { [key: string]: chrome.storage.StorageChange }) => void): void {
    if (!this.isExtensionContext()) {
      // 在非扩展环境中，监听 storage 事件
      window.addEventListener('storage', (e) => {
        if (e.key && e.newValue !== e.oldValue) {
          callback({
            [e.key]: {
              newValue: e.newValue ? JSON.parse(e.newValue) : undefined,
              oldValue: e.oldValue ? JSON.parse(e.oldValue) : undefined,
            },
          });
        }
      });
      return;
    }

    chrome.storage.onChanged.addListener(callback);
  }

  // 获取扩展 URL
  getExtensionUrl(path: string): string {
    if (!this.isExtensionContext()) {
      return path;
    }

    return chrome.runtime.getURL(path);
  }

  // 发送消息到背景脚本
  async sendMessage(message: any): Promise<any> {
    if (!this.isExtensionContext()) {
      return null;
    }

    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        resolve(response);
      });
    });
  }
}

// 创建单例实例
export const chromeService = new ChromeService();
