import React, { useState, useEffect } from 'react';
import { X, Globe, Tag, FileText } from 'lucide-react';
import { Category, AddBookmarkRequest } from '../types';
import { chromeService } from '../services/chrome';

interface AddBookmarkModalProps {
  categories: Category[];
  onAdd: (bookmark: AddBookmarkRequest) => void;
  onClose: () => void;
}

const AddBookmarkModal: React.FC<AddBookmarkModalProps> = ({
  categories,
  onAdd,
  onClose,
}) => {
  const [formData, setFormData] = useState<AddBookmarkRequest>({
    title: '',
    url: '',
    description: '',
    category_id: undefined,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 自动填充当前标签页信息
  useEffect(() => {
    const fillCurrentTab = async () => {
      try {
        const currentTab = await chromeService.getCurrentTab();
        if (currentTab && currentTab.url && currentTab.title) {
          setFormData(prev => ({
            ...prev,
            title: currentTab.title || '',
            url: currentTab.url || '',
          }));
        }
      } catch (error) {
        console.error('Failed to get current tab:', error);
      }
    };

    fillCurrentTab();
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空';
    }

    if (!formData.url.trim()) {
      newErrors.url = 'URL 不能为空';
    } else {
      try {
        new URL(formData.url);
      } catch {
        newErrors.url = '请输入有效的 URL';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    try {
      await onAdd(formData);
    } catch (error) {
      console.error('Failed to add bookmark:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUrlBlur = async () => {
    if (formData.url && !formData.title) {
      try {
        // 尝试从 URL 提取标题
        const domain = new URL(formData.url).hostname;
        setFormData(prev => ({
          ...prev,
          title: domain,
        }));
      } catch {
        // URL 无效，不做处理
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            添加书签
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* 标题 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <FileText className="w-4 h-4 inline mr-2" />
              标题
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className={`
                w-full px-3 py-2 border rounded-lg
                bg-white dark:bg-gray-700
                text-gray-900 dark:text-white
                placeholder-gray-500 dark:placeholder-gray-400
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                ${errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
              `}
              placeholder="输入书签标题"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-500">{errors.title}</p>
            )}
          </div>

          {/* URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Globe className="w-4 h-4 inline mr-2" />
              URL
            </label>
            <input
              type="url"
              value={formData.url}
              onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
              onBlur={handleUrlBlur}
              className={`
                w-full px-3 py-2 border rounded-lg
                bg-white dark:bg-gray-700
                text-gray-900 dark:text-white
                placeholder-gray-500 dark:placeholder-gray-400
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                ${errors.url ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
              `}
              placeholder="https://example.com"
            />
            {errors.url && (
              <p className="mt-1 text-sm text-red-500">{errors.url}</p>
            )}
          </div>

          {/* 分类 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Tag className="w-4 h-4 inline mr-2" />
              分类
            </label>
            <select
              value={formData.category_id || ''}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                category_id: e.target.value ? parseInt(e.target.value) : undefined 
              }))}
              className="
                w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                bg-white dark:bg-gray-700
                text-gray-900 dark:text-white
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
              "
            >
              <option value="">选择分类（可选）</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.icon} {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* 描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              描述（可选）
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="
                w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                bg-white dark:bg-gray-700
                text-gray-900 dark:text-white
                placeholder-gray-500 dark:placeholder-gray-400
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                resize-none
              "
              placeholder="添加书签描述..."
            />
          </div>

          {/* 按钮 */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="
                flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600
                text-gray-700 dark:text-gray-300
                rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700
                transition-colors
              "
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="
                flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg
                hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed
                transition-colors
              "
            >
              {loading ? '添加中...' : '添加书签'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddBookmarkModal;
