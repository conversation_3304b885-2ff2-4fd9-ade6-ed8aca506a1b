import React from 'react';
import { Search, X } from 'lucide-react';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = '搜索书签...',
  className = '',
}) => {
  return (
    <div className={`relative ${className}`}>
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-4 w-4 text-gray-400" />
      </div>
      
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="
          search-input
          block w-full pl-10 pr-10 py-2 
          border border-gray-300 dark:border-gray-600
          rounded-lg 
          bg-white/80 dark:bg-gray-800/80
          text-gray-900 dark:text-white
          placeholder-gray-500 dark:placeholder-gray-400
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
          backdrop-blur-sm
          transition-all duration-200
        "
      />
      
      {value && (
        <button
          onClick={() => onChange('')}
          className="
            absolute inset-y-0 right-0 pr-3 
            flex items-center
            text-gray-400 hover:text-gray-600 dark:hover:text-gray-300
            transition-colors
          "
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

export default SearchBar;
