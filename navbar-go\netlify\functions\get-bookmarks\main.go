// netlify/functions/get-bookmarks/main.go
package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/jackc/pgx/v5"
)

// Bookmark 结构体，用于映射数据库中的 bookmarks 表
type Bookmark struct {
	ID          int64  `json:"id"`
	Title       string `json:"title"`
	URL         string `json:"url"`
	Description string `json:"description"`
	Favicon     string `json:"favicon"`
	CategoryID  *int64 `json:"category_id"`
	SortOrder   int    `json:"sort_order"`
	IsPinned    bool   `json:"is_pinned"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// APIResponse API 响应结构体
type APIResponse struct {
	Data           []Bookmark `json:"data"`
	ConnectionTime string     `json:"connection_time_ms"`
	QueryTime      string     `json:"query_time_ms"`
	Total          int        `json:"total"`
}

func handler(request events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
	ctx := context.Background()

	// 从环境变量中安全地读取数据库连接字符串
	connString := os.Getenv("SUPABASE_DATABASE_URL")
	if connString == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "SUPABASE_DATABASE_URL not set"}`,
		}, nil
	}

	// 处理 CORS 预检请求
	if request.HTTPMethod == "OPTIONS" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 200,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: "",
		}, nil
	}

	// 测量数据库连接耗时
	connStartTime := time.Now()
	conn, err := pgx.Connect(ctx, connString)
	connDuration := time.Since(connStartTime)

	if err != nil {
		body := fmt.Sprintf(`{"error": "Unable to connect to database (took %s): %v"}`, connDuration, err)
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: body,
		}, nil
	}
	defer conn.Close(ctx)

	// 获取查询参数
	categoryID := request.QueryStringParameters["category_id"]
	
	// 构建 SQL 查询
	var query string
	var args []interface{}
	
	if categoryID != "" {
		query = `SELECT id, title, url, description, favicon, category_id, sort_order, is_pinned, 
				created_at, updated_at FROM bookmarks WHERE category_id = $1 ORDER BY sort_order, id`
		args = append(args, categoryID)
	} else {
		query = `SELECT id, title, url, description, favicon, category_id, sort_order, is_pinned, 
				created_at, updated_at FROM bookmarks ORDER BY sort_order, id`
	}

	// 测量 SQL 查询执行耗时
	queryStartTime := time.Now()
	rows, err := conn.Query(ctx, query, args...)
	queryDuration := time.Since(queryStartTime)

	if err != nil {
		body := fmt.Sprintf(`{"error": "Query failed (took %s): %v"}`, queryDuration, err)
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: body,
		}, nil
	}
	defer rows.Close()

	// 处理查询结果
	var bookmarks []Bookmark
	for rows.Next() {
		var b Bookmark
		var createdAt, updatedAt time.Time
		var description, favicon sql.NullString
		var categoryID sql.NullInt64
		var sortOrder sql.NullInt32
		var isPinned sql.NullBool

		err := rows.Scan(&b.ID, &b.Title, &b.URL, &description, &favicon,
			&categoryID, &sortOrder, &isPinned, &createdAt, &updatedAt)
		if err != nil {
			return &events.APIGatewayProxyResponse{
				StatusCode: 500,
				Headers: map[string]string{
					"Content-Type":                 "application/json",
					"Access-Control-Allow-Origin":  "*",
					"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
					"Access-Control-Allow-Headers": "Content-Type, Authorization",
				},
				Body: fmt.Sprintf(`{"error": "Failed to scan row: %v"}`, err),
			}, nil
		}

		// 处理可能为 NULL 的字段
		if description.Valid {
			b.Description = description.String
		} else {
			b.Description = ""
		}

		if favicon.Valid {
			b.Favicon = favicon.String
		} else {
			b.Favicon = ""
		}

		if categoryID.Valid {
			b.CategoryID = &categoryID.Int64
		} else {
			b.CategoryID = nil
		}

		if sortOrder.Valid {
			b.SortOrder = int(sortOrder.Int32)
		} else {
			b.SortOrder = 0
		}

		if isPinned.Valid {
			b.IsPinned = isPinned.Bool
		} else {
			b.IsPinned = false
		}

		// 格式化时间
		b.CreatedAt = createdAt.Format(time.RFC3339)
		b.UpdatedAt = updatedAt.Format(time.RFC3339)

		bookmarks = append(bookmarks, b)
	}

	if err := rows.Err(); err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Error during rows iteration: %v"}`, err),
		}, nil
	}

	// 构建响应对象
	responseObject := APIResponse{
		Data:           bookmarks,
		ConnectionTime: fmt.Sprintf("%.2fms", float64(connDuration.Microseconds())/1000.0),
		QueryTime:      fmt.Sprintf("%.2fms", float64(queryDuration.Microseconds())/1000.0),
		Total:          len(bookmarks),
	}

	// 将响应对象序列化为 JSON
	responseBody, err := json.Marshal(responseObject)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	// 返回成功的响应
	return &events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func main() {
	lambda.Start(handler)
}
