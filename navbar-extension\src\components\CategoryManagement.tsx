import React, { useState } from 'react';
import { X, Plus, Edit, Trash2, Save, Palette, Tag } from 'lucide-react';
import { Category } from '../types';

interface CategoryManagementProps {
  categories: Category[];
  onAddCategory: (data: { name: string; icon: string; color: string }) => void;
  onUpdateCategory: (id: number, data: { name?: string; icon?: string; color?: string }) => void;
  onDeleteCategory: (id: number) => void;
  onClose: () => void;
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({
  categories,
  onAddCategory,
  onUpdateCategory,
  onDeleteCategory,
  onClose,
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    icon: '📁',
    color: '#3B82F6',
  });

  const commonIcons = ['📁', '⭐', '🛠️', '📚', '🎮', '💼', '🏠', '🌐', '📱', '💻', '🎵', '🎬', '🛒', '📰', '🔧', '🎨'];
  const commonColors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899', '#6B7280', '#059669'];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      return;
    }

    if (editingId) {
      onUpdateCategory(editingId, formData);
      setEditingId(null);
    } else {
      onAddCategory(formData);
      setShowAddForm(false);
    }
    
    setFormData({ name: '', icon: '📁', color: '#3B82F6' });
  };

  const handleEdit = (category: Category) => {
    setFormData({
      name: category.name,
      icon: category.icon,
      color: category.color,
    });
    setEditingId(category.id);
    setShowAddForm(false);
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setFormData({ name: '', icon: '📁', color: '#3B82F6' });
  };

  const handleDelete = (category: Category) => {
    if (confirm(`确定要删除分类 "${category.name}" 吗？\n删除后，该分类下的书签将变为无分类状态。`)) {
      onDeleteCategory(category.id);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <Tag className="w-5 h-5 mr-2" />
            分类管理
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-6 space-y-4 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* 添加按钮 */}
          {!showAddForm && !editingId && (
            <button
              onClick={() => setShowAddForm(true)}
              className="
                w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600
                text-gray-500 dark:text-gray-400 rounded-lg
                hover:border-blue-500 hover:text-blue-500
                transition-colors flex items-center justify-center
              "
            >
              <Plus className="w-5 h-5 mr-2" />
              添加新分类
            </button>
          )}

          {/* 添加/编辑表单 */}
          {(showAddForm || editingId) && (
            <form onSubmit={handleSubmit} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg space-y-4">
              <h3 className="font-medium text-gray-900 dark:text-white">
                {editingId ? '编辑分类' : '添加分类'}
              </h3>
              
              {/* 分类名称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  分类名称
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="
                    w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                    bg-white dark:bg-gray-800
                    text-gray-900 dark:text-white
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                  "
                  placeholder="输入分类名称"
                  required
                />
              </div>

              {/* 图标选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  图标
                </label>
                <div className="grid grid-cols-8 gap-2">
                  {commonIcons.map((icon) => (
                    <button
                      key={icon}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, icon }))}
                      className={`
                        p-2 text-xl rounded-lg border-2 transition-colors
                        ${formData.icon === icon
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                        }
                      `}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>

              {/* 颜色选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  颜色
                </label>
                <div className="flex space-x-2">
                  {commonColors.map((color) => (
                    <button
                      key={color}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, color }))}
                      className={`
                        w-8 h-8 rounded-full border-2 transition-transform
                        ${formData.color === color
                          ? 'border-gray-800 dark:border-white scale-110'
                          : 'border-gray-300 dark:border-gray-600'
                        }
                      `}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              {/* 按钮 */}
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => {
                    if (editingId) {
                      handleCancelEdit();
                    } else {
                      setShowAddForm(false);
                      setFormData({ name: '', icon: '📁', color: '#3B82F6' });
                    }
                  }}
                  className="
                    flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600
                    text-gray-700 dark:text-gray-300 rounded-lg
                    hover:bg-gray-50 dark:hover:bg-gray-600
                    transition-colors
                  "
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="
                    flex-1 px-3 py-2 bg-blue-500 text-white rounded-lg
                    hover:bg-blue-600 transition-colors
                    flex items-center justify-center
                  "
                >
                  <Save className="w-4 h-4 mr-1" />
                  {editingId ? '保存' : '添加'}
                </button>
              </div>
            </form>
          )}

          {/* 分类列表 */}
          <div className="space-y-2">
            {categories.map((category) => (
              <div
                key={category.id}
                className="flex items-center justify-between p-3 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
              >
                <div className="flex items-center space-x-3">
                  <span className="text-xl">{category.icon}</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {category.name}
                  </span>
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: category.color }}
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(category)}
                    className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
                    title="编辑"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(category)}
                    className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                    title="删除"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>

          {categories.length === 0 && (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Tag className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>还没有分类，点击上方按钮添加第一个分类</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CategoryManagement;
