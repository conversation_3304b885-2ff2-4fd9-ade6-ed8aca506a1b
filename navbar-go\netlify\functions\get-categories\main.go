// netlify/functions/get-categories/main.go
package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/jackc/pgx/v5"
)

// Category 分类结构体
type Category struct {
	ID        int64  `json:"id"`
	Name      string `json:"name"`
	Icon      string `json:"icon"`
	Color     string `json:"color"`
	SortOrder int    `json:"sort_order"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}

// APIResponse API 响应结构体
type APIResponse struct {
	Data           []Category `json:"data"`
	ConnectionTime string     `json:"connection_time_ms"`
	QueryTime      string     `json:"query_time_ms"`
	Total          int        `json:"total"`
}

func handler(request events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
	ctx := context.Background()

	// 处理 CORS 预检请求
	if request.HTTPMethod == "OPTIONS" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 200,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: "",
		}, nil
	}

	// 从环境变量中读取数据库连接字符串
	connString := os.Getenv("SUPABASE_DATABASE_URL")
	if connString == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "SUPABASE_DATABASE_URL not set"}`,
		}, nil
	}

	// 测量数据库连接耗时
	connStartTime := time.Now()
	conn, err := pgx.Connect(ctx, connString)
	connDuration := time.Since(connStartTime)

	if err != nil {
		body := fmt.Sprintf(`{"error": "Unable to connect to database (took %s): %v"}`, connDuration, err)
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: body,
		}, nil
	}
	defer conn.Close(ctx)

	// 测量 SQL 查询执行耗时
	queryStartTime := time.Now()
	rows, err := conn.Query(ctx, `SELECT id, name, icon, color, sort_order, created_at, updated_at 
									FROM categories ORDER BY sort_order, id`)
	queryDuration := time.Since(queryStartTime)

	if err != nil {
		body := fmt.Sprintf(`{"error": "Query failed (took %s): %v"}`, queryDuration, err)
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: body,
		}, nil
	}
	defer rows.Close()

	// 处理查询结果
	var categories []Category
	for rows.Next() {
		var c Category
		var createdAt, updatedAt time.Time
		var icon, color sql.NullString
		var sortOrder sql.NullInt32

		err := rows.Scan(&c.ID, &c.Name, &icon, &color, &sortOrder, &createdAt, &updatedAt)
		if err != nil {
			return &events.APIGatewayProxyResponse{
				StatusCode: 500,
				Headers: map[string]string{
					"Content-Type":                 "application/json",
					"Access-Control-Allow-Origin":  "*",
					"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
					"Access-Control-Allow-Headers": "Content-Type, Authorization",
				},
				Body: fmt.Sprintf(`{"error": "Failed to scan row: %v"}`, err),
			}, nil
		}

		// 处理可能为 NULL 的字段
		if icon.Valid {
			c.Icon = icon.String
		} else {
			c.Icon = ""
		}

		if color.Valid {
			c.Color = color.String
		} else {
			c.Color = "#3B82F6" // 默认颜色
		}

		if sortOrder.Valid {
			c.SortOrder = int(sortOrder.Int32)
		} else {
			c.SortOrder = 0
		}

		// 格式化时间
		c.CreatedAt = createdAt.Format(time.RFC3339)
		c.UpdatedAt = updatedAt.Format(time.RFC3339)

		categories = append(categories, c)
	}

	if err := rows.Err(); err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Error during rows iteration: %v"}`, err),
		}, nil
	}

	// 构建响应对象
	responseObject := APIResponse{
		Data:           categories,
		ConnectionTime: fmt.Sprintf("%.2fms", float64(connDuration.Microseconds())/1000.0),
		QueryTime:      fmt.Sprintf("%.2fms", float64(queryDuration.Microseconds())/1000.0),
		Total:          len(categories),
	}

	// 将响应对象序列化为 JSON
	responseBody, err := json.Marshal(responseObject)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	// 返回成功的响应
	return &events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func main() {
	lambda.Start(handler)
}
