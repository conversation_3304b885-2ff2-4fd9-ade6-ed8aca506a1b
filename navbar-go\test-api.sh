#!/bin/bash

# API 测试脚本
# 使用方法: ./test-api.sh <your-netlify-url>
# 例如: ./test-api.sh https://amazing-site-123456.netlify.app

if [ -z "$1" ]; then
    echo "使用方法: $0 <netlify-url>"
    echo "例如: $0 https://your-site.netlify.app"
    exit 1
fi

BASE_URL="$1/.netlify/functions"

echo "🚀 测试个人导航 API 接口"
echo "基础 URL: $BASE_URL"
echo ""

# 测试获取分类
echo "📁 测试获取分类..."
curl -s -X GET "$BASE_URL/get-categories" | jq '.' || echo "❌ 获取分类失败"
echo ""

# 测试获取书签
echo "🔖 测试获取书签..."
curl -s -X GET "$BASE_URL/get-bookmarks" | jq '.' || echo "❌ 获取书签失败"
echo ""

# 测试获取设置
echo "⚙️ 测试获取设置..."
curl -s -X GET "$BASE_URL/get-settings" | jq '.' || echo "❌ 获取设置失败"
echo ""

# 测试添加书签
echo "➕ 测试添加书签..."
curl -s -X POST "$BASE_URL/add-bookmark" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试书签",
    "url": "https://example.com",
    "description": "这是一个测试书签",
    "category_id": 1
  }' | jq '.' || echo "❌ 添加书签失败"
echo ""

# 测试添加分类
echo "📂 测试添加分类..."
curl -s -X POST "$BASE_URL/manage-categories" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试分类",
    "icon": "🧪",
    "color": "#FF6B6B"
  }' | jq '.' || echo "❌ 添加分类失败"
echo ""

# 测试更新设置
echo "🔧 测试更新设置..."
curl -s -X PUT "$BASE_URL/update-settings" \
  -H "Content-Type: application/json" \
  -d '{
    "theme": "dark",
    "columns_count": 8
  }' | jq '.' || echo "❌ 更新设置失败"
echo ""

echo "✅ API 测试完成！"
echo ""
echo "💡 提示："
echo "- 如果看到 JSON 响应，说明 API 工作正常"
echo "- 如果看到错误信息，请检查："
echo "  1. Netlify 函数是否正确部署"
echo "  2. 环境变量 SUPABASE_DATABASE_URL 是否设置"
echo "  3. Supabase 数据库是否正常运行"
echo "  4. 数据库表是否正确创建"
