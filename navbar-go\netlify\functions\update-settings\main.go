// netlify/functions/update-settings/main.go
package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/jackc/pgx/v5"
)

// UpdateSettingsRequest 更新设置请求结构体
type UpdateSettingsRequest struct {
	Theme           *string `json:"theme"`
	Layout          *string `json:"layout"`
	ColumnsCount    *int    `json:"columns_count"`
	ShowSearch      *bool   `json:"show_search"`
	ShowCategories  *bool   `json:"show_categories"`
	BackgroundImage *string `json:"background_image"`
}

// UserSettings 用户设置结构体
type UserSettings struct {
	ID              int64  `json:"id"`
	Theme           string `json:"theme"`
	Layout          string `json:"layout"`
	ColumnsCount    int    `json:"columns_count"`
	ShowSearch      bool   `json:"show_search"`
	ShowCategories  bool   `json:"show_categories"`
	BackgroundImage string `json:"background_image"`
	CreatedAt       string `json:"created_at"`
	UpdatedAt       string `json:"updated_at"`
}

// APIResponse API 响应结构体
type APIResponse struct {
	Data    *UserSettings `json:"data"`
	Message string        `json:"message"`
}

func handler(request events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
	ctx := context.Background()

	// 处理 CORS 预检请求
	if request.HTTPMethod == "OPTIONS" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 200,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: "",
		}, nil
	}

	// 只允许 PUT 请求
	if request.HTTPMethod != "PUT" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 405,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "Method not allowed"}`,
		}, nil
	}

	// 从环境变量中读取数据库连接字符串
	connString := os.Getenv("SUPABASE_DATABASE_URL")
	if connString == "" {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "SUPABASE_DATABASE_URL not set"}`,
		}, nil
	}

	// 解析请求体
	var req UpdateSettingsRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Invalid JSON: %v"}`, err),
		}, nil
	}

	// 连接数据库
	conn, err := pgx.Connect(ctx, connString)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Unable to connect to database: %v"}`, err),
		}, nil
	}
	defer conn.Close(ctx)

	// 首先检查是否存在设置记录
	var settingsID int64
	err = conn.QueryRow(ctx, "SELECT id FROM user_settings ORDER BY id LIMIT 1").Scan(&settingsID)
	if err != nil {
		if err.Error() == "no rows in result set" {
			// 如果没有设置记录，先创建默认设置
			return createAndUpdateSettings(ctx, conn, req)
		}
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to check settings existence: %v"}`, err),
		}, nil
	}

	// 构建动态更新查询
	var setParts []string
	var args []interface{}
	argIndex := 1

	if req.Theme != nil && strings.TrimSpace(*req.Theme) != "" {
		// 验证主题值
		if *req.Theme != "light" && *req.Theme != "dark" {
			return &events.APIGatewayProxyResponse{
				StatusCode: 400,
				Headers: map[string]string{
					"Content-Type":                 "application/json",
					"Access-Control-Allow-Origin":  "*",
					"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
					"Access-Control-Allow-Headers": "Content-Type, Authorization",
				},
				Body: `{"error": "Theme must be 'light' or 'dark'"}`,
			}, nil
		}
		setParts = append(setParts, fmt.Sprintf("theme = $%d", argIndex))
		args = append(args, *req.Theme)
		argIndex++
	}

	if req.Layout != nil && strings.TrimSpace(*req.Layout) != "" {
		// 验证布局值
		if *req.Layout != "grid" && *req.Layout != "list" {
			return &events.APIGatewayProxyResponse{
				StatusCode: 400,
				Headers: map[string]string{
					"Content-Type":                 "application/json",
					"Access-Control-Allow-Origin":  "*",
					"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
					"Access-Control-Allow-Headers": "Content-Type, Authorization",
				},
				Body: `{"error": "Layout must be 'grid' or 'list'"}`,
			}, nil
		}
		setParts = append(setParts, fmt.Sprintf("layout = $%d", argIndex))
		args = append(args, *req.Layout)
		argIndex++
	}

	if req.ColumnsCount != nil {
		// 验证列数范围
		if *req.ColumnsCount < 2 || *req.ColumnsCount > 12 {
			return &events.APIGatewayProxyResponse{
				StatusCode: 400,
				Headers: map[string]string{
					"Content-Type":                 "application/json",
					"Access-Control-Allow-Origin":  "*",
					"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
					"Access-Control-Allow-Headers": "Content-Type, Authorization",
				},
				Body: `{"error": "Columns count must be between 2 and 12"}`,
			}, nil
		}
		setParts = append(setParts, fmt.Sprintf("columns_count = $%d", argIndex))
		args = append(args, *req.ColumnsCount)
		argIndex++
	}

	if req.ShowSearch != nil {
		setParts = append(setParts, fmt.Sprintf("show_search = $%d", argIndex))
		args = append(args, *req.ShowSearch)
		argIndex++
	}

	if req.ShowCategories != nil {
		setParts = append(setParts, fmt.Sprintf("show_categories = $%d", argIndex))
		args = append(args, *req.ShowCategories)
		argIndex++
	}

	if req.BackgroundImage != nil {
		setParts = append(setParts, fmt.Sprintf("background_image = $%d", argIndex))
		args = append(args, *req.BackgroundImage)
		argIndex++
	}

	if len(setParts) == 0 {
		return &events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: `{"error": "No fields to update"}`,
		}, nil
	}

	// 添加 updated_at 字段
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// 添加 WHERE 条件的参数
	args = append(args, settingsID)

	// 构建完整的更新查询
	query := fmt.Sprintf(`UPDATE user_settings SET %s WHERE id = $%d 
						  RETURNING id, theme, layout, columns_count, show_search, show_categories, 
						  background_image, created_at, updated_at`,
		strings.Join(setParts, ", "), argIndex)

	// 执行更新查询
	var updatedSettings UserSettings
	var createdAt, updatedAt time.Time
	var backgroundImage sql.NullString

	err = conn.QueryRow(ctx, query, args...).Scan(
		&updatedSettings.ID, &updatedSettings.Theme, &updatedSettings.Layout, &updatedSettings.ColumnsCount,
		&updatedSettings.ShowSearch, &updatedSettings.ShowCategories, &backgroundImage,
		&createdAt, &updatedAt)

	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to update settings: %v"}`, err),
		}, nil
	}

	// 处理可能为 NULL 的字段
	if backgroundImage.Valid {
		updatedSettings.BackgroundImage = backgroundImage.String
	} else {
		updatedSettings.BackgroundImage = ""
	}

	// 格式化时间
	updatedSettings.CreatedAt = createdAt.Format(time.RFC3339)
	updatedSettings.UpdatedAt = updatedAt.Format(time.RFC3339)

	// 构建响应
	response := APIResponse{
		Data:    &updatedSettings,
		Message: "Settings updated successfully",
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	return &events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func createAndUpdateSettings(ctx context.Context, conn *pgx.Conn, req UpdateSettingsRequest) (*events.APIGatewayProxyResponse, error) {
	// 创建默认设置，然后应用更新
	theme := "light"
	layout := "grid"
	columnsCount := 6
	showSearch := true
	showCategories := true
	backgroundImage := ""

	// 应用请求中的值
	if req.Theme != nil {
		theme = *req.Theme
	}
	if req.Layout != nil {
		layout = *req.Layout
	}
	if req.ColumnsCount != nil {
		columnsCount = *req.ColumnsCount
	}
	if req.ShowSearch != nil {
		showSearch = *req.ShowSearch
	}
	if req.ShowCategories != nil {
		showCategories = *req.ShowCategories
	}
	if req.BackgroundImage != nil {
		backgroundImage = *req.BackgroundImage
	}

	var newSettings UserSettings
	var createdAt, updatedAt time.Time
	var returnedBackgroundImage sql.NullString

	insertQuery := `INSERT INTO user_settings (theme, layout, columns_count, show_search, show_categories, background_image)
					VALUES ($1, $2, $3, $4, $5, $6)
					RETURNING id, theme, layout, columns_count, show_search, show_categories,
					background_image, created_at, updated_at`

	err := conn.QueryRow(ctx, insertQuery, theme, layout, columnsCount, showSearch, showCategories, backgroundImage).Scan(
		&newSettings.ID, &newSettings.Theme, &newSettings.Layout, &newSettings.ColumnsCount,
		&newSettings.ShowSearch, &newSettings.ShowCategories, &returnedBackgroundImage,
		&createdAt, &updatedAt)

	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to create settings: %v"}`, err),
		}, nil
	}

	// 处理可能为 NULL 的字段
	if returnedBackgroundImage.Valid {
		newSettings.BackgroundImage = returnedBackgroundImage.String
	} else {
		newSettings.BackgroundImage = ""
	}

	// 格式化时间
	newSettings.CreatedAt = createdAt.Format(time.RFC3339)
	newSettings.UpdatedAt = updatedAt.Format(time.RFC3339)

	// 构建响应
	response := APIResponse{
		Data:    &newSettings,
		Message: "Settings created and updated successfully",
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return &events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type":                 "application/json",
				"Access-Control-Allow-Origin":  "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
			Body: fmt.Sprintf(`{"error": "Failed to marshal response: %v"}`, err),
		}, nil
	}

	return &events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseBody),
	}, nil
}

func main() {
	lambda.Start(handler)
}
