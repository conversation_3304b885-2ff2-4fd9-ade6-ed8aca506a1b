import { Bookmark, Category, UserSettings } from '../types';
import { chromeService } from './chrome';

interface CachedData {
  bookmarks: Bookmark[];
  categories: Category[];
  settings: UserSettings;
  lastUpdated: number;
}

class StorageService {
  private readonly CACHE_KEY = 'navbar_cache';
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  // 获取缓存数据
  async getCachedData(): Promise<CachedData | null> {
    try {
      const cached = await chromeService.getStorage(this.CACHE_KEY);
      if (!cached) return null;

      // 检查缓存是否过期
      const now = Date.now();
      if (now - cached.lastUpdated > this.CACHE_DURATION) {
        await this.clearCache();
        return null;
      }

      return cached;
    } catch (error) {
      console.error('Failed to get cached data:', error);
      return null;
    }
  }

  // 保存数据到缓存
  async setCachedData(data: Omit<CachedData, 'lastUpdated'>): Promise<void> {
    try {
      const cachedData: CachedData = {
        ...data,
        lastUpdated: Date.now(),
      };
      await chromeService.setStorage(this.CACHE_KEY, cachedData);
    } catch (error) {
      console.error('Failed to cache data:', error);
    }
  }

  // 更新缓存中的书签
  async updateCachedBookmarks(bookmarks: Bookmark[]): Promise<void> {
    try {
      const cached = await this.getCachedData();
      if (cached) {
        await this.setCachedData({
          ...cached,
          bookmarks,
        });
      }
    } catch (error) {
      console.error('Failed to update cached bookmarks:', error);
    }
  }

  // 更新缓存中的分类
  async updateCachedCategories(categories: Category[]): Promise<void> {
    try {
      const cached = await this.getCachedData();
      if (cached) {
        await this.setCachedData({
          ...cached,
          categories,
        });
      }
    } catch (error) {
      console.error('Failed to update cached categories:', error);
    }
  }

  // 更新缓存中的设置
  async updateCachedSettings(settings: UserSettings): Promise<void> {
    try {
      const cached = await this.getCachedData();
      if (cached) {
        await this.setCachedData({
          ...cached,
          settings,
        });
      }
    } catch (error) {
      console.error('Failed to update cached settings:', error);
    }
  }

  // 添加书签到缓存
  async addBookmarkToCache(bookmark: Bookmark): Promise<void> {
    try {
      const cached = await this.getCachedData();
      if (cached) {
        const updatedBookmarks = [...cached.bookmarks, bookmark];
        await this.updateCachedBookmarks(updatedBookmarks);
      }
    } catch (error) {
      console.error('Failed to add bookmark to cache:', error);
    }
  }

  // 从缓存中删除书签
  async removeBookmarkFromCache(bookmarkId: number): Promise<void> {
    try {
      const cached = await this.getCachedData();
      if (cached) {
        const updatedBookmarks = cached.bookmarks.filter(b => b.id !== bookmarkId);
        await this.updateCachedBookmarks(updatedBookmarks);
      }
    } catch (error) {
      console.error('Failed to remove bookmark from cache:', error);
    }
  }

  // 更新缓存中的书签
  async updateBookmarkInCache(bookmarkId: number, updatedBookmark: Bookmark): Promise<void> {
    try {
      const cached = await this.getCachedData();
      if (cached) {
        const updatedBookmarks = cached.bookmarks.map(b => 
          b.id === bookmarkId ? updatedBookmark : b
        );
        await this.updateCachedBookmarks(updatedBookmarks);
      }
    } catch (error) {
      console.error('Failed to update bookmark in cache:', error);
    }
  }

  // 添加分类到缓存
  async addCategoryToCache(category: Category): Promise<void> {
    try {
      const cached = await this.getCachedData();
      if (cached) {
        const updatedCategories = [...cached.categories, category];
        await this.updateCachedCategories(updatedCategories);
      }
    } catch (error) {
      console.error('Failed to add category to cache:', error);
    }
  }

  // 从缓存中删除分类
  async removeCategoryFromCache(categoryId: number): Promise<void> {
    try {
      const cached = await this.getCachedData();
      if (cached) {
        const updatedCategories = cached.categories.filter(c => c.id !== categoryId);
        await this.updateCachedCategories(updatedCategories);
      }
    } catch (error) {
      console.error('Failed to remove category from cache:', error);
    }
  }

  // 更新缓存中的分类
  async updateCategoryInCache(categoryId: number, updatedCategory: Category): Promise<void> {
    try {
      const cached = await this.getCachedData();
      if (cached) {
        const updatedCategories = cached.categories.map(c => 
          c.id === categoryId ? updatedCategory : c
        );
        await this.updateCachedCategories(updatedCategories);
      }
    } catch (error) {
      console.error('Failed to update category in cache:', error);
    }
  }

  // 清除缓存
  async clearCache(): Promise<void> {
    try {
      await chromeService.setStorage(this.CACHE_KEY, null);
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }

  // 检查缓存是否有效
  async isCacheValid(): Promise<boolean> {
    const cached = await this.getCachedData();
    return cached !== null;
  }

  // 获取缓存时间戳
  async getCacheTimestamp(): Promise<number | null> {
    try {
      const cached = await chromeService.getStorage(this.CACHE_KEY);
      return cached?.lastUpdated || null;
    } catch (error) {
      console.error('Failed to get cache timestamp:', error);
      return null;
    }
  }

  // 强制刷新缓存（清除并重新获取）
  async forceRefresh(): Promise<void> {
    await this.clearCache();
  }
}

// 创建单例实例
export const storageService = new StorageService();
