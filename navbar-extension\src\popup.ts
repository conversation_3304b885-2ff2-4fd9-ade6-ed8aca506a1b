// popup.ts - Chrome 扩展弹窗脚本

interface CurrentTab {
  title?: string;
  url?: string;
  favIconUrl?: string;
}

// 获取当前标签页信息
async function getCurrentTab(): Promise<CurrentTab> {
  return new Promise((resolve) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      const tab = tabs[0];
      resolve({
        title: tab?.title || '',
        url: tab?.url || '',
        favIconUrl: tab?.favIconUrl || '',
      });
    });
  });
}

// 添加当前页面为书签
async function addCurrentPage() {
  try {
    const tab = await getCurrentTab();
    
    if (!tab.url || !tab.title) {
      alert('无法获取当前页面信息');
      return;
    }

    // 发送消息到背景脚本
    chrome.runtime.sendMessage({
      action: 'addBookmark',
      data: {
        title: tab.title,
        url: tab.url,
        favicon: tab.favIconUrl,
      },
    }, (response) => {
      if (response?.success) {
        // 显示成功消息
        showMessage('书签添加成功！', 'success');
      } else {
        showMessage('添加失败：' + (response?.error || '未知错误'), 'error');
      }
    });
  } catch (error) {
    console.error('Failed to add bookmark:', error);
    showMessage('添加失败', 'error');
  }
}

// 打开新标签页
function openNewTab() {
  chrome.tabs.create({ url: chrome.runtime.getURL('index.html') });
  window.close();
}

// 管理书签（打开设置页面）
function manageBookmarks() {
  chrome.tabs.create({ url: chrome.runtime.getURL('index.html#settings') });
  window.close();
}

// 显示消息
function showMessage(message: string, type: 'success' | 'error') {
  // 创建消息元素
  const messageEl = document.createElement('div');
  messageEl.textContent = message;
  messageEl.style.cssText = `
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 16px;
    border-radius: 4px;
    color: white;
    font-size: 12px;
    z-index: 1000;
    background: ${type === 'success' ? '#10b981' : '#ef4444'};
  `;
  
  document.body.appendChild(messageEl);
  
  // 3秒后移除
  setTimeout(() => {
    messageEl.remove();
  }, 3000);
}

// 更新当前标签页信息显示
async function updateCurrentTabInfo() {
  try {
    const tab = await getCurrentTab();
    
    const titleEl = document.getElementById('tab-title');
    const urlEl = document.getElementById('tab-url');
    
    if (titleEl && urlEl) {
      titleEl.textContent = tab.title || '无标题';
      urlEl.textContent = tab.url || '';
    }
  } catch (error) {
    console.error('Failed to get current tab info:', error);
  }
}

// 初始化弹窗
document.addEventListener('DOMContentLoaded', () => {
  // 更新当前标签页信息
  updateCurrentTabInfo();
  
  // 绑定事件监听器
  const addCurrentBtn = document.getElementById('add-current');
  const openNewtabBtn = document.getElementById('open-newtab');
  const manageBtn = document.getElementById('manage-bookmarks');
  
  if (addCurrentBtn) {
    addCurrentBtn.addEventListener('click', (e) => {
      e.preventDefault();
      addCurrentPage();
    });
  }
  
  if (openNewtabBtn) {
    openNewtabBtn.addEventListener('click', (e) => {
      e.preventDefault();
      openNewTab();
    });
  }
  
  if (manageBtn) {
    manageBtn.addEventListener('click', (e) => {
      e.preventDefault();
      manageBookmarks();
    });
  }
});

// 监听来自背景脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'bookmarkAdded') {
    showMessage('书签添加成功！', 'success');
  } else if (message.action === 'bookmarkError') {
    showMessage('添加失败：' + message.error, 'error');
  }
  
  sendResponse({ received: true });
});
