import {
  Bookmark,
  Category,
  UserSettings,
  APIResponse,
  AddBookmarkRequest,
  UpdateBookmarkRequest,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  UpdateSettingsRequest,
  BatchUpdateSortOrderRequest,
} from '../types';

// API 基础 URL - 这里需要替换为你的 Netlify 部署 URL
const API_BASE_URL = 'https://navbar-ventix.netlify.app/.netlify/functions';

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // 书签相关 API
  async getBookmarks(categoryId?: number): Promise<APIResponse<Bookmark[]>> {
    const query = categoryId ? `?category_id=${categoryId}` : '';
    return this.request<APIResponse<Bookmark[]>>(`/get-bookmarks${query}`);
  }

  async addBookmark(bookmark: AddBookmarkRequest): Promise<APIResponse<Bookmark>> {
    return this.request<APIResponse<Bookmark>>('/add-bookmark', {
      method: 'POST',
      body: JSON.stringify(bookmark),
    });
  }

  async updateBookmark(
    id: number,
    updates: UpdateBookmarkRequest
  ): Promise<APIResponse<Bookmark>> {
    return this.request<APIResponse<Bookmark>>(`/update-bookmark?id=${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteBookmark(id: number): Promise<APIResponse<{ id: number }>> {
    return this.request<APIResponse<{ id: number }>>(`/delete-bookmark?id=${id}`, {
      method: 'DELETE',
    });
  }

  // 批量更新书签排序
  async batchUpdateSortOrder(request: BatchUpdateSortOrderRequest): Promise<APIResponse<{ updated_count: number }>> {
    return this.request<APIResponse<{ updated_count: number }>>('/batch-update-sort-order', {
      method: 'PUT',
      body: JSON.stringify(request),
    });
  }

  // 分类相关 API
  async getCategories(): Promise<APIResponse<Category[]>> {
    return this.request<APIResponse<Category[]>>('/get-categories');
  }

  async createCategory(category: CreateCategoryRequest): Promise<APIResponse<Category>> {
    return this.request<APIResponse<Category>>('/manage-categories', {
      method: 'POST',
      body: JSON.stringify(category),
    });
  }

  // 添加分类的别名方法
  async addCategory(category: { name: string; icon: string; color: string }): Promise<APIResponse<Category>> {
    return this.createCategory(category);
  }

  async updateCategory(
    id: number,
    updates: UpdateCategoryRequest
  ): Promise<APIResponse<Category>> {
    return this.request<APIResponse<Category>>(`/manage-categories?id=${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteCategory(id: number): Promise<APIResponse<{ id: number }>> {
    return this.request<APIResponse<{ id: number }>>(`/manage-categories?id=${id}`, {
      method: 'DELETE',
    });
  }

  // 设置相关 API
  async getSettings(): Promise<APIResponse<UserSettings>> {
    return this.request<APIResponse<UserSettings>>('/get-settings');
  }

  async updateSettings(settings: UpdateSettingsRequest): Promise<APIResponse<UserSettings>> {
    return this.request<APIResponse<UserSettings>>('/update-settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  // 获取网站 favicon
  getFaviconUrl(url: string): string {
    try {
      const domain = new URL(url).hostname;
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
    } catch {
      return '/icons/default-favicon.png';
    }
  }

  // 从 URL 提取网站标题（用于自动填充）
  async extractTitleFromUrl(url: string): Promise<string> {
    try {
      const response = await fetch(url, { mode: 'no-cors' });
      const html = await response.text();
      const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      return titleMatch ? titleMatch[1].trim() : new URL(url).hostname;
    } catch {
      try {
        return new URL(url).hostname;
      } catch {
        return '未知网站';
      }
    }
  }
}

// 创建单例实例
export const apiService = new ApiService();

// 导出类型以便其他地方使用
export type { APIResponse };
