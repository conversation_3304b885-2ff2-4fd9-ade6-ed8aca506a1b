import React from 'react';
import { Category } from '../types';

interface CategoryFilterProps {
  categories: Category[];
  selectedCategory: number | null;
  onSelectCategory: (categoryId: number | null) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategory,
  onSelectCategory,
}) => {
  return (
    <div className="mb-8">
      <div className="flex flex-wrap gap-2">
        {/* 全部分类按钮 */}
        <button
          onClick={() => onSelectCategory(null)}
          className={`
            category-tag px-4 py-2 rounded-full text-sm font-medium
            transition-all duration-200
            ${selectedCategory === null
              ? 'bg-blue-500 text-white shadow-lg'
              : 'bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-700'
            }
            backdrop-blur-sm border border-gray-200 dark:border-gray-600
          `}
        >
          全部
        </button>

        {/* 分类按钮 */}
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => onSelectCategory(category.id)}
            className={`
              category-tag px-4 py-2 rounded-full text-sm font-medium
              transition-all duration-200 flex items-center space-x-2
              ${selectedCategory === category.id
                ? 'text-white shadow-lg'
                : 'bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-700'
              }
              backdrop-blur-sm border border-gray-200 dark:border-gray-600
            `}
            style={{
              backgroundColor: selectedCategory === category.id ? category.color : undefined,
            }}
          >
            <span className="text-base">{category.icon}</span>
            <span>{category.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryFilter;
