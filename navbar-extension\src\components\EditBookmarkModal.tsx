import React, { useState } from 'react';
import { X, Save, Link, FileText, Tag, Pin, PinOff } from 'lucide-react';
import { Bookmark, Category, UpdateBookmarkRequest } from '../types';

interface EditBookmarkModalProps {
  bookmark: Bookmark;
  categories: Category[];
  onUpdate: (id: number, updates: UpdateBookmarkRequest) => void;
  onClose: () => void;
}

const EditBookmarkModal: React.FC<EditBookmarkModalProps> = ({
  bookmark,
  categories,
  onUpdate,
  onClose,
}) => {
  const [formData, setFormData] = useState({
    title: bookmark.title,
    url: bookmark.url,
    description: bookmark.description || '',
    category_id: bookmark.category_id,
    is_pinned: bookmark.is_pinned,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空';
    }

    if (!formData.url.trim()) {
      newErrors.url = 'URL 不能为空';
    } else {
      try {
        new URL(formData.url);
      } catch {
        newErrors.url = '请输入有效的 URL';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // 只发送有变化的字段
    const updates: UpdateBookmarkRequest = {};
    
    if (formData.title !== bookmark.title) {
      updates.title = formData.title;
    }
    if (formData.url !== bookmark.url) {
      updates.url = formData.url;
    }
    if (formData.description !== (bookmark.description || '')) {
      updates.description = formData.description;
    }
    if (formData.category_id !== bookmark.category_id) {
      updates.category_id = formData.category_id;
    }
    if (formData.is_pinned !== bookmark.is_pinned) {
      updates.is_pinned = formData.is_pinned;
    }

    if (Object.keys(updates).length > 0) {
      onUpdate(bookmark.id, updates);
    }
    onClose();
  };

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            编辑书签
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* 标题 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <FileText className="w-4 h-4 inline mr-1" />
              标题 *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              className={`
                w-full px-3 py-2 border rounded-lg
                bg-white dark:bg-gray-700
                text-gray-900 dark:text-white
                placeholder-gray-500 dark:placeholder-gray-400
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                ${errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
              `}
              placeholder="输入书签标题"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-500">{errors.title}</p>
            )}
          </div>

          {/* URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Link className="w-4 h-4 inline mr-1" />
              URL *
            </label>
            <input
              type="url"
              value={formData.url}
              onChange={(e) => handleChange('url', e.target.value)}
              className={`
                w-full px-3 py-2 border rounded-lg
                bg-white dark:bg-gray-700
                text-gray-900 dark:text-white
                placeholder-gray-500 dark:placeholder-gray-400
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                ${errors.url ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
              `}
              placeholder="https://example.com"
            />
            {errors.url && (
              <p className="mt-1 text-sm text-red-500">{errors.url}</p>
            )}
          </div>

          {/* 描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              描述（可选）
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className="
                w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                bg-white dark:bg-gray-700
                text-gray-900 dark:text-white
                placeholder-gray-500 dark:placeholder-gray-400
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                resize-none
              "
              placeholder="输入书签描述"
            />
          </div>

          {/* 分类 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Tag className="w-4 h-4 inline mr-1" />
              分类
            </label>
            <select
              value={formData.category_id || ''}
              onChange={(e) => handleChange('category_id', e.target.value ? parseInt(e.target.value) : null)}
              className="
                w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                bg-white dark:bg-gray-700
                text-gray-900 dark:text-white
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
              "
            >
              <option value="">无分类</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.icon} {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* 置顶 */}
          <div className="flex items-center justify-between">
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
              {formData.is_pinned ? (
                <Pin className="w-4 h-4 mr-2 text-yellow-500" />
              ) : (
                <PinOff className="w-4 h-4 mr-2 text-gray-400" />
              )}
              置顶书签
            </label>
            <button
              type="button"
              onClick={() => handleChange('is_pinned', !formData.is_pinned)}
              className={`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${formData.is_pinned ? 'bg-yellow-500' : 'bg-gray-300 dark:bg-gray-600'}
              `}
            >
              <span
                className={`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${formData.is_pinned ? 'translate-x-6' : 'translate-x-1'}
                `}
              />
            </button>
          </div>

          {/* 按钮 */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="
                flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600
                text-gray-700 dark:text-gray-300 rounded-lg
                hover:bg-gray-50 dark:hover:bg-gray-700
                transition-colors
              "
            >
              取消
            </button>
            <button
              type="submit"
              className="
                flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg
                hover:bg-blue-600 transition-colors
                flex items-center justify-center
              "
            >
              <Save className="w-4 h-4 mr-2" />
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditBookmarkModal;
