import React, { useState, useEffect } from 'react';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent } from '@dnd-kit/core';
import { SortableContext, arrayMove } from '@dnd-kit/sortable';
import { Search, Settings, Plus, Grid, List, Moon, Sun, RefreshCw, Tag } from 'lucide-react';

import { Bookmark, Category, UserSettings, AppState, UpdateBookmarkRequest, UpdateSettingsRequest } from './types';
import { apiService } from './services/api';
import { chromeService } from './services/chrome';
import { storageService } from './services/storage';

import BookmarkCard from './components/BookmarkCard';
import CategoryFilter from './components/CategoryFilter';
import SearchBar from './components/SearchBar';
import AddBookmarkModal from './components/AddBookmarkModal';
import EditBookmarkModal from './components/EditBookmarkModal';
import SettingsModal from './components/SettingsModal';
import CategoryManagement from './components/CategoryManagement';
import LoadingSpinner from './components/LoadingSpinner';

// 获取网格样式的函数
const getGridStyle = (columnsCount: number): React.CSSProperties => {
  // 限制列数在 2-8 之间
  const columns = Math.min(Math.max(columnsCount, 2), 8);

  return {
    '--grid-cols': columns.toString(),
    gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))`
  } as React.CSSProperties;
};

function App() {
  const [state, setState] = useState<AppState>({
    bookmarks: [],
    categories: [],
    settings: {
      id: 1,
      theme: 'light',
      layout: 'grid',
      columns_count: 6,
      show_search: true,
      show_categories: true,
      background_image: '',
      created_at: '',
      updated_at: '',
    },
    loading: true,
    error: null,
    searchQuery: '',
    selectedCategory: null,
  });

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showCategoryManagement, setShowCategoryManagement] = useState(false);
  const [editingBookmark, setEditingBookmark] = useState<Bookmark | null>(null);
  const [draggedItem, setDraggedItem] = useState<Bookmark | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 初始化数据
  useEffect(() => {
    initializeApp();
  }, []);

  // 应用主题
  useEffect(() => {
    document.documentElement.classList.toggle('dark', state.settings.theme === 'dark');
  }, [state.settings.theme]);

  const initializeApp = async (forceRefresh = false) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // 如果强制刷新，清除缓存
      if (forceRefresh) {
        await storageService.forceRefresh();
      }

      // 尝试从缓存加载数据
      const cachedData = await storageService.getCachedData();

      if (cachedData && !forceRefresh) {
        // 使用缓存数据
        setState(prev => ({
          ...prev,
          bookmarks: cachedData.bookmarks,
          categories: cachedData.categories,
          settings: cachedData.settings,
          loading: false,
        }));
        return;
      }

      // 从服务器加载数据
      const [bookmarksRes, categoriesRes, settingsRes] = await Promise.all([
        apiService.getBookmarks(),
        apiService.getCategories(),
        apiService.getSettings(),
      ]);

      const newData = {
        bookmarks: bookmarksRes.data,
        categories: categoriesRes.data,
        settings: settingsRes.data,
      };

      // 保存到缓存
      await storageService.setCachedData(newData);

      setState(prev => ({
        ...prev,
        ...newData,
        loading: false,
      }));
    } catch (error) {
      console.error('Failed to initialize app:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '加载失败',
        loading: false,
      }));
    }
  };

  // 过滤书签
  const filteredBookmarks = state.bookmarks.filter(bookmark => {
    const matchesSearch = !state.searchQuery ||
      bookmark.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
      bookmark.url.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
      (bookmark.description && bookmark.description.toLowerCase().includes(state.searchQuery.toLowerCase()));

    const matchesCategory = state.selectedCategory === null ||
      bookmark.category_id === state.selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // 拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const bookmark = state.bookmarks.find(b => b.id.toString() === event.active.id);
    setDraggedItem(bookmark || null);
  };

  // 拖拽结束
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedItem(null);

    if (!over || active.id === over.id) return;

    const oldIndex = state.bookmarks.findIndex(b => b.id.toString() === active.id);
    const newIndex = state.bookmarks.findIndex(b => b.id.toString() === over.id);

    if (oldIndex === -1 || newIndex === -1) return;

    const newBookmarks = arrayMove(state.bookmarks, oldIndex, newIndex);

    // 立即更新本地状态以提供即时反馈
    setState(prev => ({ ...prev, bookmarks: newBookmarks }));

    // 计算需要更新的排序信息
    const sortOrderUpdates: Array<{ id: number; sort_order: number }> = [];

    newBookmarks.forEach((bookmark, index) => {
      if (bookmark.sort_order !== index) {
        sortOrderUpdates.push({
          id: bookmark.id,
          sort_order: index
        });
      }
    });

    // 如果有需要更新的排序，批量更新服务器
    if (sortOrderUpdates.length > 0) {
      try {
        console.log('Batch updating sort order:', sortOrderUpdates);
        await apiService.batchUpdateSortOrder({ updates: sortOrderUpdates });

        // 更新本地书签的 sort_order 字段
        const updatedBookmarks = newBookmarks.map((bookmark, index) => ({
          ...bookmark,
          sort_order: index
        }));

        setState(prev => ({ ...prev, bookmarks: updatedBookmarks }));

        // 更新缓存
        await storageService.batchUpdateBookmarkSortOrderInCache(sortOrderUpdates);

        console.log('Sort order updated successfully');
      } catch (error) {
        console.error('Failed to update bookmark sort order:', error);
        // 回滚状态
        setState(prev => ({ ...prev, bookmarks: state.bookmarks }));
      }
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await initializeApp(true);
    setIsRefreshing(false);
  };

  // 添加书签
  const handleAddBookmark = async (bookmarkData: any) => {
    try {
      const response = await apiService.addBookmark(bookmarkData);
      const newBookmarks = [...state.bookmarks, response.data];
      setState(prev => ({
        ...prev,
        bookmarks: newBookmarks,
      }));
      // 更新缓存
      await storageService.addBookmarkToCache(response.data);
      setShowAddModal(false);
    } catch (error) {
      console.error('Failed to add bookmark:', error);
    }
  };

  // 编辑书签
  const handleEditBookmark = (bookmark: Bookmark) => {
    console.log('handleEditBookmark called with:', bookmark);
    setEditingBookmark(bookmark);
    setShowEditModal(true);
  };

  // 更新书签
  const handleUpdateBookmark = async (id: number, updates: UpdateBookmarkRequest) => {
    try {
      const response = await apiService.updateBookmark(id, updates);
      const updatedBookmarks = state.bookmarks.map(b =>
        b.id === id ? response.data : b
      );
      setState(prev => ({
        ...prev,
        bookmarks: updatedBookmarks,
      }));
      // 更新缓存
      await storageService.updateBookmarkInCache(id, response.data);
      setShowEditModal(false);
      setEditingBookmark(null);
    } catch (error) {
      console.error('Failed to update bookmark:', error);
    }
  };

  // 删除书签
  const handleDeleteBookmark = async (id: number) => {
    console.log('handleDeleteBookmark called with id:', id);
    try {
      await apiService.deleteBookmark(id);
      setState(prev => ({
        ...prev,
        bookmarks: prev.bookmarks.filter(b => b.id !== id),
      }));
      // 更新缓存
      await storageService.removeBookmarkFromCache(id);
      console.log('Bookmark deleted successfully');
    } catch (error) {
      console.error('Failed to delete bookmark:', error);
    }
  };

  // 切换主题
  const toggleTheme = async () => {
    const newTheme = state.settings.theme === 'light' ? 'dark' : 'light';
    try {
      const response = await apiService.updateSettings({ theme: newTheme });
      setState(prev => ({
        ...prev,
        settings: response.data,
      }));
    } catch (error) {
      console.error('Failed to update theme:', error);
    }
  };

  // 切换布局
  const toggleLayout = async () => {
    const newLayout = state.settings.layout === 'grid' ? 'list' : 'grid';
    try {
      const response = await apiService.updateSettings({ layout: newLayout });
      setState(prev => ({
        ...prev,
        settings: response.data,
      }));
      // 更新缓存
      await storageService.updateCachedSettings(response.data);
    } catch (error) {
      console.error('Failed to update layout:', error);
    }
  };

  // 分类管理功能
  const handleAddCategory = async (data: { name: string; icon: string; color: string }) => {
    try {
      const response = await apiService.addCategory(data);
      setState(prev => ({
        ...prev,
        categories: [...prev.categories, response.data],
      }));
      // 更新缓存
      await storageService.addCategoryToCache(response.data);
    } catch (error) {
      console.error('Failed to add category:', error);
    }
  };

  const handleUpdateCategory = async (id: number, data: { name?: string; icon?: string; color?: string }) => {
    try {
      const response = await apiService.updateCategory(id, data);
      setState(prev => ({
        ...prev,
        categories: prev.categories.map(c => c.id === id ? response.data : c),
      }));
      // 更新缓存
      await storageService.updateCategoryInCache(id, response.data);
    } catch (error) {
      console.error('Failed to update category:', error);
    }
  };

  const handleDeleteCategory = async (id: number) => {
    try {
      await apiService.deleteCategory(id);
      setState(prev => ({
        ...prev,
        categories: prev.categories.filter(c => c.id !== id),
      }));
      // 更新缓存
      await storageService.removeCategoryFromCache(id);
    } catch (error) {
      console.error('Failed to delete category:', error);
    }
  };

  // 更新设置
  const handleUpdateSettings = async (updates: UpdateSettingsRequest) => {
    try {
      const response = await apiService.updateSettings(updates);
      setState(prev => ({
        ...prev,
        settings: response.data,
      }));
      // 更新缓存
      await storageService.updateCachedSettings(response.data);
    } catch (error) {
      console.error('Failed to update settings:', error);
    }
  };

  if (state.loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">加载失败</h2>
          <p className="text-gray-600 mb-4">{state.error}</p>
          <button
            onClick={() => initializeApp()}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        {/* 头部 */}
        <header className="glass sticky top-0 z-40 backdrop-blur-md">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  个人导航
                </h1>
                {state.settings.show_search && (
                  <SearchBar
                    value={state.searchQuery}
                    onChange={(value) => setState(prev => ({ ...prev, searchQuery: value }))}
                  />
                )}
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="p-2 rounded-lg hover:bg-white/20 transition-colors disabled:opacity-50"
                  title="刷新数据"
                >
                  <RefreshCw className={`w-5 h-5 text-gray-700 dark:text-gray-300 ${isRefreshing ? 'animate-spin' : ''}`} />
                </button>

                <button
                  onClick={toggleLayout}
                  className="p-2 rounded-lg hover:bg-white/20 transition-colors"
                  title={state.settings.layout === 'grid' ? '切换到列表视图' : '切换到网格视图'}
                >
                  {state.settings.layout === 'grid' ? (
                    <List className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                  ) : (
                    <Grid className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                  )}
                </button>

                <button
                  onClick={toggleTheme}
                  className="p-2 rounded-lg hover:bg-white/20 transition-colors"
                  title={state.settings.theme === 'light' ? '切换到暗色主题' : '切换到亮色主题'}
                >
                  {state.settings.theme === 'light' ? (
                    <Moon className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                  ) : (
                    <Sun className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                  )}
                </button>

                <button
                  onClick={() => setShowAddModal(true)}
                  className="p-2 rounded-lg hover:bg-white/20 transition-colors"
                  title="添加书签"
                >
                  <Plus className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                </button>

                <button
                  onClick={() => setShowCategoryManagement(true)}
                  className="p-2 rounded-lg hover:bg-white/20 transition-colors"
                  title="分类管理"
                >
                  <Tag className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                </button>

                <button
                  onClick={() => setShowSettingsModal(true)}
                  className="p-2 rounded-lg hover:bg-white/20 transition-colors"
                  title="设置"
                >
                  <Settings className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* 主要内容 */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 分类过滤器 */}
          {state.settings.show_categories && (
            <CategoryFilter
              categories={state.categories}
              selectedCategory={state.selectedCategory}
              onSelectCategory={(id) => setState(prev => ({ ...prev, selectedCategory: id }))}
            />
          )}

          {/* 书签网格/列表 */}
          <div
            className={state.settings.layout === 'grid' ? 'custom-grid' : 'space-y-4'}
            style={state.settings.layout === 'grid' ? getGridStyle(state.settings.columns_count) : undefined}
          >
            <SortableContext items={filteredBookmarks.map(b => b.id.toString())}>
              {filteredBookmarks.map((bookmark) => (
                <BookmarkCard
                  key={bookmark.id}
                  bookmark={bookmark}
                  layout={state.settings.layout}
                  onEdit={handleEditBookmark}
                  onDelete={handleDeleteBookmark}
                />
              ))}
            </SortableContext>
          </div>

          {/* 空状态 */}
          {filteredBookmarks.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {state.searchQuery ? '没有找到匹配的书签' : '还没有书签'}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                {state.searchQuery ? '尝试使用不同的关键词搜索' : '点击右上角的 + 按钮添加你的第一个书签'}
              </p>
              {!state.searchQuery && (
                <button
                  onClick={() => setShowAddModal(true)}
                  className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  添加书签
                </button>
              )}
            </div>
          )}
        </main>

        {/* 拖拽覆盖层 */}
        <DragOverlay>
          {draggedItem && (
            <div className="drag-overlay">
              <BookmarkCard
                bookmark={draggedItem}
                layout={state.settings.layout}
                onEdit={() => {}}
                onDelete={() => {}}
                isDragging
              />
            </div>
          )}
        </DragOverlay>
      </DndContext>

      {/* 模态框 */}
      {showAddModal && (
        <AddBookmarkModal
          categories={state.categories}
          onAdd={handleAddBookmark}
          onClose={() => setShowAddModal(false)}
        />
      )}

      {/* 编辑书签模态框 */}
      {showEditModal && editingBookmark && (
        <EditBookmarkModal
          bookmark={editingBookmark}
          categories={state.categories}
          onUpdate={handleUpdateBookmark}
          onClose={() => {
            setShowEditModal(false);
            setEditingBookmark(null);
          }}
        />
      )}

      {/* 分类管理模态框 */}
      {showCategoryManagement && (
        <CategoryManagement
          categories={state.categories}
          onAddCategory={handleAddCategory}
          onUpdateCategory={handleUpdateCategory}
          onDeleteCategory={handleDeleteCategory}
          onClose={() => setShowCategoryManagement(false)}
        />
      )}

      {showSettingsModal && (
        <SettingsModal
          settings={state.settings}
          onUpdateSettings={handleUpdateSettings}
          onClose={() => setShowSettingsModal(false)}
        />
      )}
    </div>
  );
}

export default App;
