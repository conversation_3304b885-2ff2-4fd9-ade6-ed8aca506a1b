<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Chrome 插件功能调试测试</h1>
    
    <div class="test-section">
        <h2>环境检测</h2>
        <button class="test-button" onclick="checkEnvironment()">检查环境</button>
        <div id="env-log" class="log-area"></div>
    </div>

    <div class="test-section">
        <h2>Chrome API 测试</h2>
        <button class="test-button" onclick="testChromeAPIs()">测试 Chrome APIs</button>
        <div id="chrome-log" class="log-area"></div>
    </div>

    <div class="test-section">
        <h2>URL 打开测试</h2>
        <button class="test-button" onclick="testOpenURL()">测试打开 URL</button>
        <div id="url-log" class="log-area"></div>
    </div>

    <div class="test-section">
        <h2>事件处理测试</h2>
        <button class="test-button" onclick="testEventHandling()">测试事件处理</button>
        <div id="event-log" class="log-area"></div>
    </div>

    <script>
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent += `[${timestamp}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        function checkEnvironment() {
            const envLog = document.getElementById('env-log');
            envLog.textContent = '';
            
            log('env-log', '开始环境检测...');
            
            // 检查 Chrome 对象
            if (typeof chrome !== 'undefined') {
                log('env-log', '✓ Chrome 对象存在');
                
                if (chrome.runtime) {
                    log('env-log', '✓ chrome.runtime 存在');
                    if (chrome.runtime.id) {
                        log('env-log', `✓ chrome.runtime.id: ${chrome.runtime.id}`);
                    } else {
                        log('env-log', '✗ chrome.runtime.id 不存在');
                    }
                } else {
                    log('env-log', '✗ chrome.runtime 不存在');
                }
                
                if (chrome.tabs) {
                    log('env-log', '✓ chrome.tabs 存在');
                } else {
                    log('env-log', '✗ chrome.tabs 不存在');
                }
                
                if (chrome.storage) {
                    log('env-log', '✓ chrome.storage 存在');
                } else {
                    log('env-log', '✗ chrome.storage 不存在');
                }
            } else {
                log('env-log', '✗ Chrome 对象不存在');
            }
            
            // 检查扩展环境
            const isExtension = typeof chrome !== 'undefined' && !!chrome.runtime && !!chrome.runtime.id;
            log('env-log', `扩展环境检测: ${isExtension ? '是扩展环境' : '不是扩展环境'}`);
        }

        function testChromeAPIs() {
            const chromeLog = document.getElementById('chrome-log');
            chromeLog.textContent = '';
            
            log('chrome-log', '开始测试 Chrome APIs...');
            
            if (typeof chrome === 'undefined') {
                log('chrome-log', '✗ Chrome 对象不存在，无法测试');
                return;
            }
            
            // 测试 chrome.tabs.create
            if (chrome.tabs && chrome.tabs.create) {
                log('chrome-log', '✓ chrome.tabs.create 可用');
                
                // 尝试创建标签页
                try {
                    chrome.tabs.create({ url: 'https://www.google.com' }, (tab) => {
                        if (chrome.runtime.lastError) {
                            log('chrome-log', `✗ 创建标签页失败: ${chrome.runtime.lastError.message}`);
                        } else {
                            log('chrome-log', `✓ 标签页创建成功: ${tab ? tab.id : 'unknown'}`);
                        }
                    });
                } catch (error) {
                    log('chrome-log', `✗ 创建标签页异常: ${error.message}`);
                }
            } else {
                log('chrome-log', '✗ chrome.tabs.create 不可用');
            }
        }

        function testOpenURL() {
            const urlLog = document.getElementById('url-log');
            urlLog.textContent = '';
            
            log('url-log', '开始测试 URL 打开...');
            
            const testUrl = 'https://www.google.com';
            
            // 测试 window.open
            log('url-log', '测试 window.open...');
            try {
                const newWindow = window.open(testUrl, '_blank');
                if (newWindow) {
                    log('url-log', '✓ window.open 成功');
                } else {
                    log('url-log', '✗ window.open 被阻止（可能是弹窗拦截）');
                }
            } catch (error) {
                log('url-log', `✗ window.open 异常: ${error.message}`);
            }
        }

        function testEventHandling() {
            const eventLog = document.getElementById('event-log');
            eventLog.textContent = '';
            
            log('event-log', '开始测试事件处理...');
            
            // 创建测试按钮
            const testContainer = document.createElement('div');
            testContainer.innerHTML = `
                <div style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">
                    <button id="test-edit-btn" style="margin: 5px;">编辑按钮</button>
                    <button id="test-delete-btn" style="margin: 5px;">删除按钮</button>
                    <div id="test-card" style="border: 1px solid #ddd; padding: 10px; margin: 5px; cursor: pointer;">
                        点击卡片区域
                    </div>
                </div>
            `;
            
            document.getElementById('event-log').parentNode.appendChild(testContainer);
            
            // 绑定事件
            document.getElementById('test-edit-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                log('event-log', '✓ 编辑按钮点击事件触发');
            });
            
            document.getElementById('test-delete-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                log('event-log', '✓ 删除按钮点击事件触发');
            });
            
            document.getElementById('test-card').addEventListener('click', (e) => {
                if (e.target.tagName === 'BUTTON') {
                    log('event-log', '卡片点击被按钮阻止');
                    return;
                }
                log('event-log', '✓ 卡片点击事件触发');
            });
            
            log('event-log', '测试按钮已创建，请点击测试');
        }

        // 页面加载时自动检查环境
        window.addEventListener('load', () => {
            checkEnvironment();
        });
    </script>
</body>
</html>
