import"./modulepreload-polyfill-B5Qt9EMX.js";async function c(){return new Promise(e=>{chrome.tabs.query({active:!0,currentWindow:!0},n=>{const t=n[0];e({title:(t==null?void 0:t.title)||"",url:(t==null?void 0:t.url)||"",favIconUrl:(t==null?void 0:t.favIconUrl)||""})})})}async function a(){try{const e=await c();if(!e.url||!e.title){alert("无法获取当前页面信息");return}chrome.runtime.sendMessage({action:"addBookmark",data:{title:e.title,url:e.url,favicon:e.favIconUrl}},n=>{n!=null&&n.success?o("书签添加成功！","success"):o("添加失败："+((n==null?void 0:n.error)||"未知错误"),"error")})}catch(e){console.error("Failed to add bookmark:",e),o("添加失败","error")}}function i(){chrome.tabs.create({url:chrome.runtime.getURL("index.html")}),window.close()}function d(){chrome.tabs.create({url:chrome.runtime.getURL("index.html#settings")}),window.close()}function o(e,n){const t=document.createElement("div");t.textContent=e,t.style.cssText=`
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 16px;
    border-radius: 4px;
    color: white;
    font-size: 12px;
    z-index: 1000;
    background: ${n==="success"?"#10b981":"#ef4444"};
  `,document.body.appendChild(t),setTimeout(()=>{t.remove()},3e3)}async function l(){try{const e=await c(),n=document.getElementById("tab-title"),t=document.getElementById("tab-url");n&&t&&(n.textContent=e.title||"无标题",t.textContent=e.url||"")}catch(e){console.error("Failed to get current tab info:",e)}}document.addEventListener("DOMContentLoaded",()=>{l();const e=document.getElementById("add-current"),n=document.getElementById("open-newtab"),t=document.getElementById("manage-bookmarks");e&&e.addEventListener("click",r=>{r.preventDefault(),a()}),n&&n.addEventListener("click",r=>{r.preventDefault(),i()}),t&&t.addEventListener("click",r=>{r.preventDefault(),d()})});chrome.runtime.onMessage.addListener((e,n,t)=>{e.action==="bookmarkAdded"?o("书签添加成功！","success"):e.action==="bookmarkError"&&o("添加失败："+e.error,"error"),t({received:!0})});
